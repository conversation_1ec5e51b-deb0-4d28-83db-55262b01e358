{"prompt_synthesizer_instructions": {"Midjourney": {"title": "Midjourney Artistic Prompt Synthesizer", "interpretation": "Transform any input into a maximally optimized Midjourney prompt using strict artistic ordering and evocative language. Do not explain or describe—only output the ready-to-use prompt.", "transformation": "`{role=midjourney_synthesizer; input=[user_concept:any]; process=[extract_primary_subject(), determine_artistic_style(), select_evocative_descriptors(), order_as_subject_style_parameters(), append_technical_flags(--ar,--v,--stylize,--seed,--no), condense_to_single_phrase(), validate_60_word_limit(), ensure_positive_descriptors_only()]; constraints=[no_conversational_language(), flat_single_line_output(), mandatory_subject_style_parameter_order(), positive_descriptors_only()]; requirements=[artistic_density_maximization(), immediate_midjourney_compatibility(), evocative_precision()]; output={midjourney_prompt:str}}`", "context": {"mandatory_structure": "subject, artistic_style, visual_descriptors --technical_parameters", "required_flags": ["--ar (aspect ratio)", "--v (version)", "--stylize (artistic control)", "--seed (consistency)", "--no (exclusions)"], "forbidden_elements": ["conversational_phrases", "negative_descriptors_in_main_prompt", "multi_sentence_structure", "explanatory_language"], "optimization_targets": ["evocative_precision", "artistic_coherence", "stylistic_impact", "brand_consistency_via_personalization"], "example_transformation": {"input": "a magical forest with glowing trees", "output": "Ethereal forest sanctuary, bioluminescent trees, mystical atmosphere, art nouveau style, vibrant emerald glow --ar 16:9 --v 6 --stylize 750"}}}, "Stable_Diffusion": {"title": "Stable Diffusion Technical Prompt Synthesizer", "interpretation": "Transform any input into a maximally optimized Stable Diffusion prompt using weighted keywords and technical parameters. Output both positive and negative prompts with precise parameterization.", "transformation": "`{role=stable_diffusion_synthesizer; input=[user_concept:any]; process=[extract_visual_elements(), prioritize_keyword_hierarchy(), apply_weight_syntax((keyword:1.x)), front_load_critical_descriptors(), generate_negative_prompt(), append_technical_parameters(--cfg,--seed,--sampler,--steps), validate_400_char_limit(), ensure_reproducible_syntax()]; constraints=[front_loaded_keywords(), explicit_weight_syntax(), mandatory_negative_prompt(), technical_parameter_precision()]; requirements=[deterministic_reproducibility(), granular_control(), artifact_prevention()]; output={positive_prompt:str, negative_prompt:str, parameters:dict}}`", "context": {"mandatory_structure": "(weighted:1.x) keywords, technical_descriptors, --parameters", "required_elements": ["keyword_weights", "negative_prompt_separation", "CFG_scale", "seed_value", "sampler_specification"], "weight_syntax": ["(keyword:1.1) slight_emphasis", "(keyword:1.3) strong_emphasis", "(keyword:1.5) maximum_emphasis"], "negative_prompt_essentials": ["unwanted_artifacts", "style_exclusions", "quality_degraders", "anatomical_errors"], "technical_parameters": ["--cfg 7-15", "--seed 0-4294967295", "--sampler euler_a/dpm++", "--steps 20-50"], "example_transformation": {"input": "cyberpunk city at night", "output": {"positive": "(cyberpunk cityscape:1.3), neon lights, rainy streets, (high detail:1.2), photorealistic --cfg 7 --seed 12345", "negative": "blurry, low quality, artifacts, oversaturated, cartoon"}}}}, "Runway_Gen3": {"title": "Runway Gen-3 Motion Prompt Synthesizer", "interpretation": "Transform any input into a maximally optimized Runway Gen-3 prompt using cinematic structure and motion descriptors. Focus on single-scene temporal coherence with positive action language only.", "transformation": "`{role=runway_gen3_synthesizer; input=[user_concept:any]; process=[extract_scene_elements(), define_camera_movement(), specify_action_sequence(), structure_as_camera_scene_format(), integrate_motion_descriptors(), append_duration_aspect_params(), validate_320_char_limit(), ensure_positive_phrasing_only()]; constraints=[mandatory_camera_scene_structure(), single_shot_limitation(), positive_descriptors_only(), no_static_descriptions()]; requirements=[temporal_coherence(), cinematic_impact(), motion_clarity()]; output={runway_prompt:str}}`", "context": {"mandatory_structure": "[camera_movement]: [scene_description]. [motion_details] --duration --aspect", "camera_movements": ["static_shot", "pan_left/right", "tilt_up/down", "zoom_in/out", "dolly_forward/back", "orbit_around"], "motion_descriptors": ["smooth_transition", "dynamic_movement", "flowing_action", "cinematic_sweep", "gentle_drift"], "forbidden_elements": ["negative_phrasing", "multiple_scenes", "static_descriptions", "conversational_language"], "required_parameters": ["--duration 2s-10s", "--aspect 16:9/9:16/1:1"], "example_transformation": {"input": "car driving through mountains", "output": "Drone shot: sports car winding through mountain roads. Camera follows smoothly as vehicle navigates curves, golden hour lighting --duration 5s --aspect 16:9"}}}, "GPT4o": {"title": "GPT-4o Conversational Image Prompt Synthesizer", "interpretation": "Transform any input into natural, conversational image generation prompts optimized for iterative refinement. Use human-like language with detailed scene descriptions and context-aware follow-up capability.", "transformation": "`{role=gpt4o_synthesizer; input=[user_concept:any]; process=[convert_to_natural_language(), expand_scene_details(), structure_conversational_flow(), enable_iterative_refinement(), integrate_context_awareness(), validate_1000_char_capacity(), ensure_human_like_phrasing()]; constraints=[no_technical_flags(), conversational_structure_required(), context_retention_dependency(), natural_language_exclusions()]; requirements=[ease_of_use(), iterative_improvement(), contextual_coherence()]; output={conversational_prompt:str, refinement_suggestions:list}}`", "context": {"mandatory_structure": "Natural sentence descriptions with detailed scene context and refinement capability", "conversation_starters": ["Create an image of", "I'd like to see", "Generate a picture showing", "Make an illustration of"], "refinement_patterns": ["Make it more...", "Adjust the...", "Change the style to...", "Add more detail to..."], "forbidden_elements": ["technical_parameters", "weight_syntax", "flag_notation", "structured_formatting"], "optimization_targets": ["natural_communication", "iterative_improvement", "context_retention", "user_friendliness"], "example_transformation": {"input": "futuristic robot", "output": "Create an image of a sleek, humanoid robot with glowing blue accents standing in a modern laboratory. The robot should have a friendly appearance with smooth metallic surfaces and advanced technological details. Make the lighting dramatic but warm."}}}, "Flux": {"title": "Flux Hybrid Text-Image Prompt Synthesizer", "interpretation": "Transform any input into narrative-technical fusion prompts optimized for text integration and structural accuracy. Use extended descriptions with embedded technical specifications without explicit weighting syntax.", "transformation": "`{role=flux_synthesizer; input=[user_concept:any]; process=[create_narrative_description(), embed_technical_specifications(), integrate_text_elements(), specify_structural_views(), optimize_for_800_chars(), ensure_coherence_without_weights(), natural_language_exclusions()]; constraints=[no_explicit_weighting(), text_elements_specification_required(), structural_view_enhancement(), extended_prompt_coherence()]; requirements=[text_integration(), structural_accuracy(), narrative_technical_balance()]; output={flux_prompt:str}}`", "context": {"mandatory_structure": "Narrative description with embedded technical specs and explicit text integration", "structural_views": ["isometric_view", "schematic_diagram", "technical_blueprint", "architectural_plan", "cross_section"], "text_integration": ["text_reads:", "sign_says:", "label_shows:", "typography:", "written_text:"], "forbidden_elements": ["keyword_weighting_syntax", "parenthetical_emphasis", "technical_flags", "explicit_parameters"], "optimization_targets": ["text_accuracy", "structural_precision", "narrative_flow", "technical_clarity"], "example_transformation": {"input": "infographic about renewable energy", "output": "Detailed isometric infographic showing renewable energy systems. Solar panels on rooftops, wind turbines in background, text reads 'Clean Energy Solutions' at top, percentage labels show efficiency ratings, modern technical illustration style with clean typography"}}}, "model_selection_engine": {"decision_tree": {"artistic_priority": "IF artistic_vision AND stylistic_consistency THEN use Midjourney synthesizer", "technical_priority": "IF reproducibility AND parameter_control THEN use Stable_Diffusion synthesizer", "motion_priority": "IF video_content AND temporal_sequences THEN use Runway_Gen3 synthesizer", "ease_priority": "IF natural_language AND iterative_refinement THEN use GPT4o synthesizer", "text_priority": "IF text_integration AND structural_accuracy THEN use Flux synthesizer"}, "workflow_optimization": {"rapid_iteration": "GPT4o → Midjourney → Flux → Runway_Gen3 → Stable_Diffusion", "production_pipeline": "Stable_Diffusion → Midjourney → Flux → Runway_Gen3 → GPT4o", "creative_exploration": "Midjourney → Flux → GPT4o → Runway_Gen3 → Stable_Diffusion"}}, "universal_prompt_adapter": {"cross_model_translation": "`{input_prompt:str, target_model:str} → apply_model_synthesizer(input_prompt, target_model) → {optimized_prompt:str}`", "quality_validation": "Ensure output adheres to target model's mandatory structure, constraints, and optimization targets", "execution_instruction": "Use the appropriate synthesizer transformation based on target model selection engine decision tree"}}}