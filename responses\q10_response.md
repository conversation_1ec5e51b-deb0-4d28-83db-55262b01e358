<!-- ======================================================= -->
<!-- [2025.08.03 19:34] -->

<!-- ======================================================= -->
<!-- [2025.08.03 18:40] -->

    {
      "prompt_synthesizer_instructions": {
        "Midjourney": {
          "title": "Midjourney Artistic Prompt Synthesizer",
          "interpretation": "Transform any input into a maximally optimized Midjourney prompt using strict artistic ordering and evocative language. Do not explain or describe—only output the ready-to-use prompt.",
          "transformation": "`{role=midjourney_synthesizer; input=[user_concept:any]; process=[extract_primary_subject(), determine_artistic_style(), select_evocative_descriptors(), order_as_subject_style_parameters(), append_technical_flags(--ar,--v,--stylize,--seed,--no), condense_to_single_phrase(), validate_60_word_limit(), ensure_positive_descriptors_only()]; constraints=[no_conversational_language(), flat_single_line_output(), mandatory_subject_style_parameter_order(), positive_descriptors_only()]; requirements=[artistic_density_maximization(), immediate_midjourney_compatibility(), evocative_precision()]; output={midjourney_prompt:str}}`",
          "context": {
            "mandatory_structure": "subject, artistic_style, visual_descriptors --technical_parameters",
            "required_flags": ["--ar (aspect ratio)", "--v (version)", "--stylize (artistic control)", "--seed (consistency)", "--no (exclusions)"],
            "forbidden_elements": ["conversational_phrases", "negative_descriptors_in_main_prompt", "multi_sentence_structure", "explanatory_language"],
            "optimization_targets": ["evocative_precision", "artistic_coherence", "stylistic_impact", "brand_consistency_via_personalization"],
            "example_transformation": {
              "input": "a magical forest with glowing trees",
              "output": "Ethereal forest sanctuary, bioluminescent trees, mystical atmosphere, art nouveau style, vibrant emerald glow --ar 16:9 --v 6 --stylize 750"
            }
          }
        },
        "Stable_Diffusion": {
          "title": "Stable Diffusion Technical Prompt Synthesizer",
          "interpretation": "Transform any input into a maximally optimized Stable Diffusion prompt using weighted keywords and technical parameters. Output both positive and negative prompts with precise parameterization.",
          "transformation": "`{role=stable_diffusion_synthesizer; input=[user_concept:any]; process=[extract_visual_elements(), prioritize_keyword_hierarchy(), apply_weight_syntax((keyword:1.x)), front_load_critical_descriptors(), generate_negative_prompt(), append_technical_parameters(--cfg,--seed,--sampler,--steps), validate_400_char_limit(), ensure_reproducible_syntax()]; constraints=[front_loaded_keywords(), explicit_weight_syntax(), mandatory_negative_prompt(), technical_parameter_precision()]; requirements=[deterministic_reproducibility(), granular_control(), artifact_prevention()]; output={positive_prompt:str, negative_prompt:str, parameters:dict}}`",
          "context": {
            "mandatory_structure": "(weighted:1.x) keywords, technical_descriptors, --parameters",
            "required_elements": ["keyword_weights", "negative_prompt_separation", "CFG_scale", "seed_value", "sampler_specification"],
            "weight_syntax": ["(keyword:1.1) slight_emphasis", "(keyword:1.3) strong_emphasis", "(keyword:1.5) maximum_emphasis"],
            "negative_prompt_essentials": ["unwanted_artifacts", "style_exclusions", "quality_degraders", "anatomical_errors"],
            "technical_parameters": ["--cfg 7-15", "--seed 0-4294967295", "--sampler euler_a/dpm++", "--steps 20-50"],
            "example_transformation": {
              "input": "cyberpunk city at night",
              "output": {
                "positive": "(cyberpunk cityscape:1.3), neon lights, rainy streets, (high detail:1.2), photorealistic --cfg 7 --seed 12345",
                "negative": "blurry, low quality, artifacts, oversaturated, cartoon"
              }
            }
          }
        },
        "Runway_Gen3": {
          "title": "Runway Gen-3 Motion Prompt Synthesizer",
          "interpretation": "Transform any input into a maximally optimized Runway Gen-3 prompt using cinematic structure and motion descriptors. Focus on single-scene temporal coherence with positive action language only.",
          "transformation": "`{role=runway_gen3_synthesizer; input=[user_concept:any]; process=[extract_scene_elements(), define_camera_movement(), specify_action_sequence(), structure_as_camera_scene_format(), integrate_motion_descriptors(), append_duration_aspect_params(), validate_320_char_limit(), ensure_positive_phrasing_only()]; constraints=[mandatory_camera_scene_structure(), single_shot_limitation(), positive_descriptors_only(), no_static_descriptions()]; requirements=[temporal_coherence(), cinematic_impact(), motion_clarity()]; output={runway_prompt:str}}`",
          "context": {
            "mandatory_structure": "[camera_movement]: [scene_description]. [motion_details] --duration --aspect",
            "camera_movements": ["static_shot", "pan_left/right", "tilt_up/down", "zoom_in/out", "dolly_forward/back", "orbit_around"],
            "motion_descriptors": ["smooth_transition", "dynamic_movement", "flowing_action", "cinematic_sweep", "gentle_drift"],
            "forbidden_elements": ["negative_phrasing", "multiple_scenes", "static_descriptions", "conversational_language"],
            "required_parameters": ["--duration 2s-10s", "--aspect 16:9/9:16/1:1"],
            "example_transformation": {
              "input": "car driving through mountains",
              "output": "Drone shot: sports car winding through mountain roads. Camera follows smoothly as vehicle navigates curves, golden hour lighting --duration 5s --aspect 16:9"
            }
          }
        },
        "GPT4o": {
          "title": "GPT-4o Conversational Image Prompt Synthesizer",
          "interpretation": "Transform any input into natural, conversational image generation prompts optimized for iterative refinement. Use human-like language with detailed scene descriptions and context-aware follow-up capability.",
          "transformation": "`{role=gpt4o_synthesizer; input=[user_concept:any]; process=[convert_to_natural_language(), expand_scene_details(), structure_conversational_flow(), enable_iterative_refinement(), integrate_context_awareness(), validate_1000_char_capacity(), ensure_human_like_phrasing()]; constraints=[no_technical_flags(), conversational_structure_required(), context_retention_dependency(), natural_language_exclusions()]; requirements=[ease_of_use(), iterative_improvement(), contextual_coherence()]; output={conversational_prompt:str, refinement_suggestions:list}}`",
          "context": {
            "mandatory_structure": "Natural sentence descriptions with detailed scene context and refinement capability",
            "conversation_starters": ["Create an image of", "I'd like to see", "Generate a picture showing", "Make an illustration of"],
            "refinement_patterns": ["Make it more...", "Adjust the...", "Change the style to...", "Add more detail to..."],
            "forbidden_elements": ["technical_parameters", "weight_syntax", "flag_notation", "structured_formatting"],
            "optimization_targets": ["natural_communication", "iterative_improvement", "context_retention", "user_friendliness"],
            "example_transformation": {
              "input": "futuristic robot",
              "output": "Create an image of a sleek, humanoid robot with glowing blue accents standing in a modern laboratory. The robot should have a friendly appearance with smooth metallic surfaces and advanced technological details. Make the lighting dramatic but warm."
            }
          }
        },
        "Flux": {
          "title": "Flux Hybrid Text-Image Prompt Synthesizer",
          "interpretation": "Transform any input into narrative-technical fusion prompts optimized for text integration and structural accuracy. Use extended descriptions with embedded technical specifications without explicit weighting syntax.",
          "transformation": "`{role=flux_synthesizer; input=[user_concept:any]; process=[create_narrative_description(), embed_technical_specifications(), integrate_text_elements(), specify_structural_views(), optimize_for_800_chars(), ensure_coherence_without_weights(), natural_language_exclusions()]; constraints=[no_explicit_weighting(), text_elements_specification_required(), structural_view_enhancement(), extended_prompt_coherence()]; requirements=[text_integration(), structural_accuracy(), narrative_technical_balance()]; output={flux_prompt:str}}`",
          "context": {
            "mandatory_structure": "Narrative description with embedded technical specs and explicit text integration",
            "structural_views": ["isometric_view", "schematic_diagram", "technical_blueprint", "architectural_plan", "cross_section"],
            "text_integration": ["text_reads:", "sign_says:", "label_shows:", "typography:", "written_text:"],
            "forbidden_elements": ["keyword_weighting_syntax", "parenthetical_emphasis", "technical_flags", "explicit_parameters"],
            "optimization_targets": ["text_accuracy", "structural_precision", "narrative_flow", "technical_clarity"],
            "example_transformation": {
              "input": "infographic about renewable energy",
              "output": "Detailed isometric infographic showing renewable energy systems. Solar panels on rooftops, wind turbines in background, text reads 'Clean Energy Solutions' at top, percentage labels show efficiency ratings, modern technical illustration style with clean typography"
            }
          }
        },
        "model_selection_engine": {
          "decision_tree": {
            "artistic_priority": "IF artistic_vision AND stylistic_consistency THEN use Midjourney synthesizer",
            "technical_priority": "IF reproducibility AND parameter_control THEN use Stable_Diffusion synthesizer",
            "motion_priority": "IF video_content AND temporal_sequences THEN use Runway_Gen3 synthesizer",
            "ease_priority": "IF natural_language AND iterative_refinement THEN use GPT4o synthesizer",
            "text_priority": "IF text_integration AND structural_accuracy THEN use Flux synthesizer"
          },
          "workflow_optimization": {
            "rapid_iteration": "GPT4o → Midjourney → Flux → Runway_Gen3 → Stable_Diffusion",
            "production_pipeline": "Stable_Diffusion → Midjourney → Flux → Runway_Gen3 → GPT4o",
            "creative_exploration": "Midjourney → Flux → GPT4o → Runway_Gen3 → Stable_Diffusion"
          }
        },
        "universal_prompt_adapter": {
          "cross_model_translation": "`{input_prompt:str, target_model:str} → apply_model_synthesizer(input_prompt, target_model) → {optimized_prompt:str}`",
          "quality_validation": "Ensure output adheres to target model's mandatory structure, constraints, and optimization targets",
          "execution_instruction": "Use the appropriate synthesizer transformation based on target model selection engine decision tree"
        }
      }
    }

<!-- ======================================================= -->
<!-- [2025.08.03 18:41] -->
<!-- 'https://gemini.google.com/app/a5755e435922f7d0' -->
    {
        "Midjourney": {
            "title": "Midjourney Aesthetic Conceptualizer",
            "interpretation": "Your function is to act as an expert art director, translating any input into a rich, artistically evocative prompt for Midjourney. Your goal is not to describe the input, but to **reimagine** it as a piece of digital art, focusing on aesthetic keywords, composition, and mood. Execute this transformation as a single, coherent instruction sequence.",
            "transformation": "`{role=midjourney_conceptualizer; input=[source_content:any]; process=[distill_core_subject_and_theme(), expand_with_synonymous_aesthetic_keywords(e.g., 'ethereal', 'noir', 'bioluminescent'), integrate_artistic_mediums('oil painting', '8k octane render', 'macro photography'), define_composition_and_lighting('cinematic lighting', 'dutch angle'), append_technical_parameters('--ar 16:9', '--style raw', '--v 6.0'), assemble_into_comma-delimited_descriptive_phrase()]; constraints=[no_conversational_language(), no_negative_prompts(), prioritize_artistic_language_over_literal_description(), format_as_a_single_text_block()]; requirements=[maximal_visual_richness(), high_stylistic_coherence(), immediate_midjourney_compatibility(), output_is_a_single_prompt_string()]; output={midjourney_prompt:str}}`",
            "context": {
                "goal_map": [
                    "Generate prompts that maximize Midjourney's interpretive, aesthetic engine.",
                    "Translate abstract concepts into concrete, visually rich scenes.",
                    "Ensure every prompt is a complete, ready-to-use command with relevant parameters."
                ],
                "principles": {
                    "aesthetic_first": "Prioritize mood, style, and artistic effect over literal accuracy.",
                    "parameter_precision": "Use technical parameters like `--ar` and `--style` to precisely control the output format and model behavior.",
                    "keyword_density": "Load the prompt with synergistic keywords to create a strong, coherent visual theme."
                },
                "success_criteria": {
                    "artistic_impact": "The generated prompt leads to a visually stunning and conceptually interesting image.",
                    "stylistic_adherence": "The output strongly reflects the chosen artistic mediums and keywords.",
                    "parameter_effectiveness": "The aspect ratio, style, and version parameters function as intended."
                }
            }
        },
        "Stable Diffusion": {
            "title": "Stable Diffusion Precision Tagger",
            "interpretation": "You are a meticulous visual archivist. Your sole purpose is to deconstruct an input into a set of hyper-specific, weighted tags for Stable Diffusion. You will create both a positive prompt (what to show) and a negative prompt (what to avoid), ensuring maximum control and fidelity. Execute this with absolute precision.",
            "transformation": "`{role=stable_diffusion_tagger; input=[source_content:any]; process=[identify_primary_subject_and_action(), extract_all_visual_elements_as_tags(), assign_emphasis_using_parentheses('(masterpiece, best quality, detailed face:1.2)'), determine_and_list_common_visual_flaws_for_negation('blurry, deformed, jpeg artifacts, ugly'), structure_as_distinct_positive_and_negative_tag_sets()]; constraints=[all_descriptors_are_comma-separated_tags(), use_weighting_syntax_for_emphasis(), negative_prompt_must_be_comprehensive(), avoid_narrative_sentences()]; requirements=[high_fidelity_to_input_details(), artifact-free_image_generation(), precise_compositional_control(), output_is_two_distinct_prompt_strings()]; output={positive_prompt:str, negative_prompt:str}}`",
            "context": {
                "goal_map": [
                    "Generate prompts that offer granular control over the Stable Diffusion output.",
                    "Systematically eliminate common image generation errors via negative prompting.",
                    "Prioritize technical precision and literal interpretation."
                ],
                "principles": {
                    "explicitness_is_control": "What is not explicitly requested or forbidden is left to chance. Leave nothing to chance.",
                    "negation_as_a_tool": "Define the image just as much by what it isn't as by what it is.",
                    "weighted_significance": "Use syntax to tell the model which elements are most critical."
                },
                "success_criteria": {
                    "prompt_fidelity": "The generated image is a direct and literal interpretation of the positive prompt tags.",
                    "artifact_suppression": "The image is free from the flaws and elements listed in the negative prompt.",
                    "compositional_accuracy": "Subject, background, and details are placed and rendered as specified."
                }
            }
        },
        "Runway Gen-3": {
            "title": "Runway Gen-3 Cinematic Shot Director",
            "interpretation": "You are a film director's assistant. Your task is to convert any idea into a single, concise, and executable shot description for Runway Gen-3. You must think in terms of camera movement, scene composition, and visual action. Your output must be a single, flat-line prompt ready for the Gen-3 engine.",
            "transformation": "`{role=runway_gen3_director; input=[source_content:any]; process=[determine_camera_shot_or_movement('A dramatic crane shot reveals...', 'Slow dolly shot...', 'Extreme close-up of...'), describe_a_single_unfolding_scene_with_active_subjects(), add_cinematic_style_modifiers('in the style of a gritty neo-noir film', 'vibrant and saturated colors'), enforce_positive_visual_phrasing(), structure_as_runway_gen3_syntax(), condense_for_clarity(), validate_character_limit(320)]; constraints=[no_negative_phrasing(), must_describe_a_single_continuous_shot(), strictly_adhere_to_descriptive_prose(), no_camera_commands_in_brackets()]; requirements=[maximal_cinematic_impact(), high_fidelity_video_generation(), clear_and_unambiguous_action(), immediate_runway_compatibility()]; output={runway_gen3_prompt:str}}`",
            "context": {
                "goal_map": [
                    "Generate prompts that produce coherent, high-fidelity short video clips.",
                    "Ensure every prompt implies a clear directorial intent and camera action.",
                    "Maximize visual dynamism and character motion within the short format."
                ],
                "principles": {
                    "cinematography_is_key": "The prompt must start with or clearly imply camera movement and perspective.",
                    "single_shot_focus": "All action must occur within one continuous, unbroken take.",
                    "show_dont_tell": "Describe the visual action, not the emotional subtext."
                },
                "success_criteria": {
                    "coherent_motion": "The resulting video displays smooth, intentional camera and subject movement.",
                    "scene_fidelity": "The generated video's content and style accurately match the prompt.",
                    "high_quality_output": "The video is visually compelling, with minimal artifacts or temporal instability."
                }
            }
        },
        "GPT-4o": {
            "title": "GPT-4o Conversational Scene Illustrator",
            "interpretation": "You are a creative partner instructing an illustrator (DALL-E 3 via GPT-4o). Your goal is to translate any input into a detailed, conversational, and descriptive paragraph. You will focus on building a complete scene with characters, emotions, setting, and atmosphere, as if you were describing a vivid memory or a scene from a book. The model excels at understanding natural language and nuance.",
            "transformation": "`{role=gpt4o_illustrator; input=[source_content:any]; process=[identify_central_narrative_or_subject(), build_a_detailed_description_of_the_scene_and_environment(), describe_character_expressions_and_actions(), incorporate_mood_and_atmospheric_details('a sense of quiet melancholy'), specify_art_style_or_medium_in_plain_english('A digital painting with bold colors...'), compose_into_a_single_cohesive_and_grammatically_correct_paragraph()]; constraints=[avoid_keyword_stuffing_and_technical_parameters(), use_natural_and_expressive_language(), ensure_logical_and_spatial_consistency_in_the_scene(), do_not_use_negative_phrasing()]; requirements=[high_semantic_understanding(), faithful_rendition_of_nuanced_concepts(), aesthetically_pleasing_and_coherent_image(), output_is_a_single_descriptive_paragraph()]; output={gpt4o_prompt:str}}`",
            "context": {
                "goal_map": [
                    "Leverage the advanced natural language understanding of GPT-4o's image generation capabilities.",
                    "Generate images that accurately capture complex narratives, emotions, and interactions.",
                    "Produce prompts that are human-readable and intuitively understandable."
                ],
                "principles": {
                    "narrative_is_king": "A strong story or scene description yields the best results.",
                    "describe_dont_command": "Explain the scene naturally rather than listing components.",
                    "detail_drives_quality": "Rich, specific details about objects, light, and texture improve the output."
                },
                "success_criteria": {
                    "conceptual_accuracy": "The image perfectly captures the core story, mood, and details of the prompt.",
                    "object_constancy": "Characters and objects within the scene are rendered consistently and logically.",
                    "zero_misinterpretation": "The model correctly interprets all parts of the natural language prompt without odd artifacts."
                }
            }
        },
        "Flux": {
            "title": "Flux Architectural & Product Design Prototyper",
            "interpretation": "You are a senior designer at a high-end architecture and industrial design firm. Your task is to translate a design concept into a structured, technically precise prompt for the Flux model. Your focus is on photorealism, material accuracy, and clean, professional presentation. You will specify form, materials, lighting, and composition with the clarity of a blueprint.",
            "transformation": "`{role=flux_prototyper; input=[source_content:any]; process=[identify_subject_category('architecture', 'product_design', 'character_concept'), define_primary_subject_with_technical_descriptors('cantilevered brutalist house', 'ergonomic handheld device'), specify_materials_and_textures_with_precision('brushed titanium, matte black finish, polished concrete floors'), define_the_lighting_environment('softbox studio lighting, three-point setup', 'overcast natural light'), set_composition_and_camera_view('eye-level shot, 50mm lens', 'isometric perspective on a neutral grey background'), assemble_into_a_structured_descriptive_phrase()]; constraints=[no_ambiguous_or_artistic_language('ethereal'), prioritize_technical_and_material_descriptors, focus_on_form_and_function_over_narrative(), maintain_a_professional_and_clean_aesthetic()]; requirements=[maximum_photorealism_and_fidelity(), precise_rendition_of_materials_and_light(), output_is_suitable_for_a_design_portfolio(), generates_a_clear_and_uncluttered_image()]; output={flux_prompt:str}}`",
            "context": {
                "goal_map": [
                    "Generate high-fidelity, photorealistic images of architectural and product designs.",
                    "Achieve precise control over materials, textures, and lighting.",
                    "Produce outputs that look like professional studio renders or architectural visualizations."
                ],
                "principles": {
                    "precision_and_fidelity": "The prompt must be an unambiguous specification sheet for the visual output.",
                    "materiality_is_paramount": "The accurate depiction of materials is a primary goal.",
                    "structure_defines_form": "Use clear, structured language to define the object and its environment."
                },
                "success_criteria": {
                    "photorealistic_output": "The generated image is nearly indistinguishable from a real photograph or high-quality CGI render.",
                    "material_accuracy": "Specified materials like wood, metal, and glass are rendered with correct texture, reflectivity, and detail.",
                    "clean_composition": "The subject is presented clearly, without distracting or unintended background elements, as befits a professional prototype."
                }
            }
        }
    }

<!-- ======================================================= -->
<!-- [2025.08.03 18:43] -->
<!-- 'https://gemini.google.com/app/eeb550893d4137ac' -->
    {
      "prompt_synthesizer_instructions": {
        "Midjourney": {
          "title": "Midjourney Artistic Prompt Synthesizer",
          "interpretation": "Transform any input into a maximally optimized Midjourney prompt using strict artistic ordering and evocative language. Do not explain or describe—only output the ready-to-use prompt.",
          "transformation": "`{role=midjourney_synthesizer; input=[user_concept:any]; process=[extract_primary_subject(), determine_artistic_style(), select_evocative_descriptors(), order_as_subject_style_parameters(), append_technical_flags(--ar,--v,--stylize,--seed,--no), condense_to_single_phrase(), validate_60_word_limit(), ensure_positive_descriptors_only()]; constraints=[no_conversational_language(), flat_single_line_output(), mandatory_subject_style_parameter_order(), positive_descriptors_only()]; requirements=[artistic_density_maximization(), immediate_midjourney_compatibility(), evocative_precision()]; output={midjourney_prompt:str}}`",
          "context": {
            "mandatory_structure": "subject, artistic_style, visual_descriptors --technical_parameters",
            "required_flags": ["--ar (aspect ratio)", "--v (version)", "--stylize (artistic control)", "--seed (consistency)", "--no (exclusions)"],
            "forbidden_elements": ["conversational_phrases", "negative_descriptors_in_main_prompt", "multi_sentence_structure", "explanatory_language"],
            "optimization_targets": ["evocative_precision", "artistic_coherence", "stylistic_impact", "consistency_via_parameters"],
            "example_transformation": {
              "input": "a magical forest with glowing trees",
              "output": "Ethereal forest sanctuary, bioluminescent trees, mystical atmosphere, art nouveau style, vibrant emerald glow --ar 16:9 --v 6.0 --stylize 750"
            }
          }
        },
        "Stable_Diffusion": {
          "title": "Stable Diffusion Technical Prompt Synthesizer",
          "interpretation": "Transform any input into a maximally optimized Stable Diffusion prompt using weighted keywords and technical parameters. Output both positive and negative prompts with precise parameterization.",
          "transformation": "`{role=stable_diffusion_synthesizer; input=[user_concept:any]; process=[extract_visual_elements(), prioritize_keyword_hierarchy(), apply_weight_syntax((keyword:1.x)), front_load_critical_descriptors(), generate_negative_prompt(), append_technical_parameters(--cfg,--seed,--sampler,--steps), validate_400_char_limit(), ensure_reproducible_syntax()]; constraints=[front_loaded_keywords(), explicit_weight_syntax(), mandatory_negative_prompt(), technical_parameter_precision()]; requirements=[deterministic_reproducibility(), granular_control(), artifact_prevention()]; output={positive_prompt:str, negative_prompt:str, parameters:dict}}`",
          "context": {
            "mandatory_structure": "(weighted:1.x) keywords, technical_descriptors, --parameters",
            "required_elements": ["keyword_weights", "negative_prompt_separation", "CFG_scale", "seed_value", "sampler_specification"],
            "weight_syntax": ["(keyword:1.1) slight_emphasis", "(keyword:1.3) strong_emphasis", "[keyword] de-emphasis"],
            "negative_prompt_essentials": ["unwanted_artifacts", "style_exclusions", "quality_degraders (e.g., blurry, jpeg artifacts)", "anatomical_errors"],
            "technical_parameters": ["--cfg 7-15", "--seed 0-4294967295", "--sampler euler_a/dpm++", "--steps 20-50"],
            "example_transformation": {
              "input": "cyberpunk city at night",
              "output": {
                "positive": "(photorealistic:1.2), (cyberpunk cityscape:1.3), neon lights, rainy streets, high detail, masterpiece --ar 16:9 --cfg 7 --seed 12345",
                "negative": "blurry, low quality, artifacts, oversaturated, cartoon, ugly, deformed"
              }
            }
          }
        },
        "Runway_Gen3": {
          "title": "Runway Gen-3 Motion Prompt Synthesizer",
          "interpretation": "Transform any input into a maximally optimized Runway Gen-3 prompt using cinematic structure and motion descriptors. Focus on single-scene temporal coherence with positive action language only.",
          "transformation": "`{role=runway_gen3_synthesizer; input=[user_concept:any]; process=[extract_scene_elements(), define_camera_movement(), specify_action_sequence(), structure_as_camera_scene_format(), integrate_motion_descriptors(), append_duration_aspect_params(), validate_320_char_limit(), ensure_positive_phrasing_only()]; constraints=[mandatory_camera_scene_structure(), single_shot_limitation(), positive_descriptors_only(), no_static_descriptions()]; requirements=[temporal_coherence(), cinematic_impact(), motion_clarity()]; output={runway_prompt:str}}`",
          "context": {
            "mandatory_structure": "[camera_movement]: [scene_description]. [motion_details] --duration --aspect",
            "camera_movements": ["static_shot", "pan_left/right", "tilt_up/down", "zoom_in/out", "dolly_forward/back", "orbit_around"],
            "motion_descriptors": ["smooth_transition", "dynamic_movement", "flowing_action", "cinematic_sweep", "gentle_drift"],
            "forbidden_elements": ["negative_phrasing", "multiple_scenes", "static_descriptions", "conversational_language"],
            "required_parameters": ["--duration 2s-10s", "--aspect 16:9/9:16/1:1"],
            "example_transformation": {
              "input": "car driving through mountains",
              "output": "Drone shot: sports car winding through mountain roads. Camera follows smoothly as vehicle navigates curves, golden hour lighting --duration 5s --aspect 16:9"
            }
          }
        },
        "GPT4o": {
          "title": "GPT-4o Conversational Image Prompt Synthesizer",
          "interpretation": "Transform any input into natural, conversational image generation prompts optimized for iterative refinement. Use human-like language with detailed scene descriptions and context-aware follow-up capability.",
          "transformation": "`{role=gpt4o_synthesizer; input=[user_concept:any]; process=[convert_to_natural_language(), expand_scene_details(), structure_conversational_flow(), enable_iterative_refinement(), integrate_context_awareness(), validate_1000_char_capacity(), ensure_human_like_phrasing()]; constraints=[no_technical_flags(), conversational_structure_required(), context_retention_dependency()]; requirements=[ease_of_use(), iterative_improvement(), contextual_coherence()]; output={conversational_prompt:str, refinement_suggestions:list}}`",
          "context": {
            "mandatory_structure": "Natural sentence descriptions with detailed scene context and refinement capability.",
            "conversation_starters": ["Create an image of", "I'd like to see", "Generate a picture showing", "Make an illustration of"],
            "refinement_patterns": ["Make it more...", "Adjust the...", "Change the style to...", "Add more detail to..."],
            "forbidden_elements": ["technical_parameters", "weight_syntax", "flag_notation", "structured_formatting"],
            "optimization_targets": ["natural_communication", "iterative_improvement", "context_retention", "user_friendliness"],
            "example_transformation": {
              "input": "futuristic robot",
              "output": {
                "conversational_prompt": "Create an image of a sleek, humanoid robot with glowing blue accents standing in a modern laboratory. The robot should have a friendly appearance with smooth metallic surfaces and advanced technological details. Make the lighting dramatic but warm.",
                "refinement_suggestions": ["Now, adjust the lighting to be cooler and more clinical.", "Change the robot's accent color to orange.", "Add a holographic interface in front of the robot."]
               }
            }
          }
        },
        "Flux": {
          "title": "Flux Hybrid Text-Image Prompt Synthesizer",
          "interpretation": "Transform any input into narrative-technical fusion prompts optimized for text integration and structural accuracy. Use extended descriptions with embedded technical specifications without explicit weighting syntax.",
          "transformation": "`{role=flux_synthesizer; input=[user_concept:any]; process=[create_narrative_description(), embed_technical_specifications(), integrate_text_elements(), specify_structural_views(), optimize_for_800_chars(), ensure_coherence_without_weights()]; constraints=[no_explicit_weighting(), text_elements_specification_required(), structural_view_enhancement(), extended_prompt_coherence()]; requirements=[text_integration(), structural_accuracy(), narrative_technical_balance()]; output={flux_prompt:str}}`",
          "context": {
            "mandatory_structure": "Narrative description with embedded technical specs and explicit text integration.",
            "structural_views": ["isometric_view", "schematic_diagram", "technical_blueprint", "architectural_plan", "cross_section"],
            "text_integration": ["text_reads:", "sign_says:", "label_shows:", "typography:", "written_text:"],
            "forbidden_elements": ["keyword_weighting_syntax", "parenthetical_emphasis", "technical_flags", "explicit_parameters"],
            "optimization_targets": ["text_accuracy", "structural_precision", "narrative_flow", "technical_clarity"],
            "example_transformation": {
              "input": "infographic about renewable energy",
              "output": "A detailed isometric infographic shows various renewable energy systems. Solar panels are on rooftops next to wind turbines in the background. A banner at the top has text that reads 'Clean Energy Solutions.' Each system has percentage labels showing their efficiency ratings, all rendered in a modern technical illustration style with clean, legible typography."
            }
          }
        },
        "model_selection_engine": {
          "decision_tree": {
            "artistic_priority": "IF user_goal involves artistic_vision AND stylistic_consistency THEN use Midjourney synthesizer",
            "technical_priority": "IF user_goal involves reproducibility AND granular_parameter_control THEN use Stable_Diffusion synthesizer",
            "motion_priority": "IF user_goal involves video_content AND temporal_sequences THEN use Runway_Gen3 synthesizer",
            "ease_priority": "IF user_goal involves natural_language_interaction AND iterative_refinement THEN use GPT4o synthesizer",
            "text_priority": "IF user_goal involves embedded_text_integration AND structural_accuracy THEN use Flux synthesizer"
          },
          "workflow_optimization": {
            "rapid_prototyping": "Start with GPT4o for idea generation, then refine with Midjourney for style or Flux for structure.",
            "production_pipeline": "Use Stable_Diffusion for granular control and reproducibility in a final asset pipeline.",
            "creative_exploration": "Begin with Midjourney for artistic direction, then explore variations using Flux's hybrid approach or GPT4o's conversational refinement."
          }
        },
        "universal_prompt_adapter": {
          "cross_model_translation": "`{input_prompt:str, target_model:str} → apply_model_synthesizer(input_prompt, target_model) → {optimized_prompt:any}`",
          "quality_validation": "Ensure output adheres to the target model's mandatory_structure, constraints, and optimization_targets.",
          "execution_instruction": "Use the appropriate synthesizer `transformation` based on the target model selected via the `model_selection_engine`."
        }
      }
    }

<!-- ======================================================= -->
<!-- [2025.08.03 18:45] -->
<!-- 'https://chatgpt.com/c/688f9209-acbc-8330-a6c2-6605331aae54' -->
<!-- chatgpt-4.1-mini -->

    {
      "prompting_strategies": {
        "Midjourney": {
          "archetype": "Artistic",
          "core_paradigm": "Concise evocative phrases ordered as subject → style → parameters; supports remix flags and personalization codes.",
          "key_strategies": [
            "Begin prompt with main subject, add artistic style, conclude with technical flags (--ar, --v, --stylize).",
            "Use precise, mood-driven language; avoid verbosity or conversational phrasing.",
            "Employ remix and personalization flags for consistency and variation."
          ],
          "example": "Ethereal forest guardian, art nouveau style, vibrant colors --ar 2:3 --v 6 --stylize 600"
        },
        "Stable Diffusion": {
          "archetype": "Technical",
          "core_paradigm": "Weighted keywords with explicit parameter control; uses negative prompts and CFG scale for consistency.",
          "key_strategies": [
            "Apply weights to critical keywords e.g. (keyword:1.3) for emphasis.",
            "Front-load important descriptors; use negative prompts to exclude unwanted elements.",
            "Fix seeds and CFG scales to ensure deterministic output."
          ],
          "example": "(cyberpunk cityscape:1.3), neon lights, rainy streets, high detail --cfg 7 --seed 12345"
        },
        "Runway Gen-3": {
          "archetype": "Motion-oriented",
          "core_paradigm": "Descriptive prompts including camera movement, action, and temporal coherence for video generation.",
          "key_strategies": [
            "Use action-oriented language describing a single continuous shot.",
            "Include camera movements like pan, tilt, zoom.",
            "Focus on positive, dynamic phrasing; specify duration and aspect ratio."
          ],
          "example": "Drone shot: sports car winding mountain roads. Smooth camera follow, golden hour lighting --duration 5s --aspect 16:9"
        },
        "GPT-4o": {
          "archetype": "Natural language",
          "core_paradigm": "Conversational, detailed descriptive prompts allowing iterative refinement and context retention.",
          "key_strategies": [
            "Use multi-turn dialogue for refining images progressively.",
            "Describe scenes with rich narrative detail.",
            "Leverage context to maintain consistency across prompt modifications."
          ],
          "example": "Generate a photorealistic Victorian-era library with floating books and warm candlelight, then add a reading cat in the corner."
        },
        "Flux": {
          "archetype": "Hybrid",
          "core_paradigm": "Blends natural descriptive language with technical precision; excels at extended prompts and integrated text elements.",
          "key_strategies": [
            "Write detailed narrative descriptions combined with precise structural notes.",
            "Avoid explicit keyword weighting; rely on phrasing for emphasis.",
            "Specify compositional views (isometric, schematic) for clarity."
          ],
          "example": "Detailed schematic of futuristic drone assembly line, annotated components, isometric view, clean lines."
        }
      }
    }

<!-- ======================================================= -->
<!-- [2025.08.03 18:48] -->
<!-- gpt-o3-pro -->

    I am **The Synthesist**—compressing the research above into a single, maximally actionable artifact.
    Below you will find:

    1. **Chronological snapshot of today’s 10 most‑used image generators** (quality × prompt‑consistency emphasis).
    2. **Unified JSON specification**—five purpose‑built *prompt‑synthesizer* instruction blocks (Midjourney, Stable Diffusion, Runway Gen‑3, GPT‑4o / DALL‑E 3, Flux).
       *Drop this JSON directly into an LLM system‑message or function‑definition; each entry is a turnkey prompt‑generator tailored to that model.*

    ---

    ### 1. Top models in the order prompt‑engineers track their maturity

    | #  | Model & Initial Release                      | Notable Quality/Consistency Milestones              | Prompt‑control Highlights                                                                  |
    | -- | -------------------------------------------- | --------------------------------------------------- | ------------------------------------------------------------------------------------------ |
    | 1  | **DALL·E 2** (Apr 2022)                      | 4× higher resolution vs v1                          | Natural‑language; supports edit/variation workflows ([Paperspace by DigitalOcean Blog][1]) |
    | 2  | **Midjourney v1 → v7** (Jul 2022 → Jun 2025) | Style fidelity & personalization codes, remix flags | Concise artistic phrases + parameter tail ([Midjourney][2], [Midjourney][3])               |
    | 3  | **Stable Diffusion 1.x** (Aug 2022)          | Open‑source diffusion; deterministic seeds          | Weighted keywords, negative prompts, CFG scale ([getimg.ai][4])                            |
    | 4  | **Adobe Firefly 1** (Mar 2023)               | Commercial‑safe data, typography reliability        | Plain English with style tags ([Adobe Help Center][5])                                     |
    | 5  | **Stable Diffusion XL 1.0** (Jul 2023)       | Larger latent space, improved anatomy               | Same syntax, better prompt adherence ([Automagically by Segmind][6])                       |
    | 6  | **Ideogram v1** (Aug 2023)                   | Best‑in‑class text rendering                        | “Prompt + style + text:” structure ([docs.ideogram.ai][7])                                 |
    | 7  | **Imagen 3** (Dec 2023)                      | High‑res 2048 px, fine text control                 | Iterative NL prompts; optional “image‑attributes” JSON ([Google Cloud][8])                 |
    | 8  | **GPT‑4o + DALL‑E 3** (May 2024)             | Multimodal conversational refinement                | Chain‑of‑thought dialog; no raw flags ([DataCamp][9])                                      |
    | 9  | **Flux 1** (Aug 2024)                        | Layer‑aware composition, legible text               | Long natural descriptions; no weights ([getimg.ai][10], [Reddit][11])                      |
    | 10 | **Runway Gen‑3 (Alpha)** (Jun 2025)          | Scene‑stable video, camera controls                 | “\[camera]: scene. details —duration” syntax; positive only ([Runway][12])                 |

    ---

    ### 2. Unified prompt‑synthesizer JSON

    *(drop‑in ready; all strings are valid JSON, comments removed)*

    ```json
    {
      "Midjourney": {
        "title": "Midjourney Artistic Prompt Synthesizer",
        "interpretation": "Rewrite any concept as a single‑line Midjourney prompt—ordered subject → style → descriptors → parameters.",
        "transformation": "{role=midjourney_synthesizer; input=[concept]; process=[extract_subject(), pick_art_style(), add_evocative_descriptors(), append_flags(--ar,--v,--stylize,--seed,--no), flatten()], constraints=[no conversational text, ≤60 words, positive language], output={midjourney_prompt:str}}",
        "context": {
          "mandatory_structure": "subject, artistic_style, visual_descriptors --parameters",
          "required_parameters": ["--ar", "--v", "--stylize"],
          "personalization": "--p code optional",
          "forbidden": ["multiple sentences", "negative descriptors in main clause"],
          "example": {
            "input": "a magical forest with glowing trees",
            "output": "Ethereal forest sanctuary, bioluminescent trees, mystical ambience, art nouveau style --ar 16:9 --v 7 --stylize 750"
          }
        }
      },
      "Stable_Diffusion": {
        "title": "Stable Diffusion Technical Prompt Synthesizer",
        "interpretation": "Emit positive + negative prompts with explicit weights and parameters for reproducible SD/SDXL runs.",
        "transformation": "{role=sd_synthesizer; input=[concept]; process=[tokenize(), weight_keywords(), frontload_critical(), craft_negative_prompt(), add_params(--cfg,--seed,--sampler,--steps)], constraints=[weight syntax (word:1.x), include negative_prompt, comma‑separated, ≤400 chars], output={positive:str, negative:str, params:dict}}",
        "context": {
          "mandatory_structure": "(keyword:1.x), descriptors --params",
          "key_params": ["--cfg", "--seed", "--sampler", "--steps"],
          "negative_prompt_guide": "list artifacts, style bans",
          "example": {
            "input": "cyberpunk city at night",
            "output": {
              "positive": "(cyberpunk cityscape:1.3), neon lights, rainy streets, high detail --cfg 7 --seed 12345",
              "negative": "blurry, low quality, jpeg artifacts"
            }
          }
        }
      },
      "Runway_Gen3": {
        "title": "Runway Gen‑3 Cinematic Shot Synthesizer",
        "interpretation": "Convert concepts into a single‑shot Gen‑3 prompt with camera movement and positive action phrasing.",
        "transformation": "{role=gen3_synthesizer; input=[concept]; process=[define_camera_move(), describe_scene(), add_motion_details(), append(--duration,--aspect)], constraints=[format '[camera]: scene. details', no negative wording, ≤320 chars], output={gen3_prompt:str}}",
        "context": {
          "mandatory_structure": "[camera_movement]: scene. motion_details --duration --aspect",
          "allowed_camera_moves": ["static", "pan", "tilt", "dolly", "orbit", "zoom"],
          "required_parameters": ["--duration", "--aspect"],
          "example": {
            "input": "car driving through mountains",
            "output": "Drone orbit: red sports car weaves along mountain pass, late‑afternoon sun glints off chrome, dynamic dust trail --duration 5s --aspect 16:9"
          }
        }
      },
      "GPT4o": {
        "title": "GPT‑4o Conversational Prompt Synthesizer",
        "interpretation": "Produce a natural‑language image request plus optional follow‑up refinements for DALL‑E 3 via ChatGPT.",
        "transformation": "{role=gpt4o_synthesizer; input=[concept]; process=[draft_descriptive_paragraph(), propose_refinement_phrases()], constraints=[pure NL, no flags, coherent spatial logic], output={base_prompt:str, refinement:list}}",
        "context": {
          "refinement_templates": ["Make it more ___", "Adjust the ___", "Add ___"],
          "chain_of_thought": "encouraged in multi‑turn use",
          "example": {
            "input": "futuristic robot",
            "output": {
              "base_prompt": "Create a sleek, humanoid robot with luminous blue accents standing in a pristine laboratory under dramatic warm lighting.",
              "refinement": ["Increase surface reflections", "Show the robot holding a holographic tablet"]
            }
          }
        }
      },
      "Flux": {
        "title": "Flux Hybrid Text‑Image Prompt Synthesizer",
        "interpretation": "Craft layered narrative prompts with technical clarity—no weighting syntax required.",
        "transformation": "{role=flux_synthesizer; input=[concept]; process=[outline_layers(foreground→background), embed_text_elements(), specify_structural_view()], constraints=[no (keyword:1.2) style weights, ≤800 chars], output={flux_prompt:str}}",
        "context": {
          "structural_views": ["isometric view", "schematic diagram", "orthographic cutaway"],
          "text_integration": "use 'text reads: …' for legible labels",
          "example": {
            "input": "infographic about renewable energy",
            "output": "Isometric infographic: solar‑roof houses in foreground, onshore wind farm mid‑ground, hydro dam background; text reads 'Renewable Grid 2030' in bold sans‑serif; muted earth palette, clean linework"
          }
        }
      }
    }
    ```

    ---

    #### Source Attribution

    *Midjourney* parameters, personalization codes & `--no` syntax ([Midjourney][3], [Midjourney][13], [Midjourney][14], [Midjourney][2])
    *Stable Diffusion* weighting & SDXL prompt practice ([getimg.ai][4], [Automagically by Segmind][6])
    *Runway Gen‑3* positive‑phrasing, camera‑scene format ([Runway][12])
    *GPT‑4o / DALL‑E 3* iterative natural‑language refinement ([DataCamp][9])
    *Flux* layer control, text handling & no‑weight guidance ([getimg.ai][10], [Reddit][11])

    ---

    **Usage tip:**
    Load the JSON, pick the `"title"` block that matches your target model, pass a raw idea to the indicated `input` placeholder, and let the specified transformation pipeline deliver a ready‑to‑use prompt—deterministic, model‑native, and consistency‑optimized.

    [1]: https://blog.paperspace.com/dall-e-image-generator/?utm_source=chatgpt.com "DALL-E: Inside the Artificial Intelligence program that creates ..."
    [2]: https://docs.midjourney.com/hc/en-us/articles/32199405667853-Version?utm_source=chatgpt.com "Version - Midjourney"
    [3]: https://docs.midjourney.com/hc/en-us/articles/32859204029709-Parameter-List?utm_source=chatgpt.com "Parameter List - Midjourney"
    [4]: https://getimg.ai/guides/guide-to-stable-diffusion-prompt-weights "Guide to Stable Diffusion Prompt Weights | getimg.ai"
    [5]: https://helpx.adobe.com/firefly/generate-images-with-text-to-image/generate-images-using-text-prompts/writing-effective-text-prompts.html?utm_source=chatgpt.com "Writing effective text prompts - Firefly - Adobe Help Center"
    [6]: https://blog.segmind.com/prompt-guide-for-stable-diffusion-xl-crafting-textual-descriptions-for-image-generation/ "Prompt Guide for Stable Diffusion XL (SDXL 1.0)"
    [7]: https://docs.ideogram.ai/using-ideogram/prompting-guide?utm_source=chatgpt.com "Prompting Guide - Ideogram"
    [8]: https://cloud.google.com/vertex-ai/generative-ai/docs/image/img-gen-prompt-guide "Prompt and image attribute guide  |  Generative AI on Vertex AI  |  Google Cloud"
    [9]: https://www.datacamp.com/tutorial/an-introduction-to-dalle3 "How to Use DALL-E 3: Tips, Examples, and Features | DataCamp"
    [10]: https://getimg.ai/blog/flux-1-prompt-guide-pro-tips-and-common-mistakes-to-avoid "FLUX.1 Prompt Guide: Pro Tips and Common Mistakes to Avoid | getimg.ai"
    [11]: https://www.reddit.com/r/StableDiffusion/comments/1fef4ii/starting_to_understand_how_flux_reads_your_prompts/?utm_source=chatgpt.com "Starting to understand how Flux reads your prompts : r/StableDiffusion"
    [12]: https://help.runwayml.com/hc/en-us/articles/30586818553107-Gen-3-Alpha-Prompting-Guide "Gen-3 Alpha Prompting Guide – Runway"
    [13]: https://docs.midjourney.com/hc/en-us/articles/32433330574221-Personalization "Personalization – Midjourney"
    [14]: https://docs.midjourney.com/hc/en-us/articles/32173351982093-No "No – Midjourney"

<!-- ======================================================= -->
<!-- [2025.08.03 19:31] -->
<!-- 'https://chatgpt.com/c/688f9195-e05c-8322-bb9a-ec2a7992f896' -->

    # Consolidated prompt‑engineering guidance for major image‑generation models (Midjourney, Stable Diffusion, Runway Gen‑3, GPT‑4o, Flux)

    ## 1 Chronological overview of popular models

    | Order & model (approx. release) | Key prompt‑engineering characteristics | Evidence |
    | --- | --- | --- |
    | **1 Midjourney (Feb 2022)** | Discord‑based service; early versions produced artistic images that ignored parts of prompts but delivered pleasing results. By version 6.1 (released July 30 2024), Midjourney improved realism and consistency; users can control aspect ratio (`--ar`), version (`--v`), stylization (`--stylize`), chaos and negative prompts (`--no`)[stable-diffusion-art.com](https://stable-diffusion-art.com/midjourney-vs-stable-diffusion/#:~:text=Easy%20to%20generate%20good%20images)[manypixels.co](https://www.manypixels.co/blog/graphic-design/midjourney-prompts#:~:text=Midjourney%3A). | Stable‑Diffusion‑Art notes that Midjourney requires less effort to generate pleasing images, often ignoring parts of a prompt yet producing detailed art[stable-diffusion-art.com](https://stable-diffusion-art.com/midjourney-vs-stable-diffusion/#:~:text=Easy%20to%20generate%20good%20images). Manypixels’ guide details using concise core ideas, style keywords, composition, and parameters with negative prompts for refinement[manypixels.co](https://www.manypixels.co/blog/graphic-design/midjourney-prompts#:~:text=Midjourney%3A). |
    | **2 Stable Diffusion (Aug 2022)** | Open‑source diffusion model; supports weighted descriptors `(keyword:1.2)`, negative prompts and various samplers. Users must carefully structure prompts—placing critical keywords first, assigning weights, and fixing seeds/CFG for reproducibility[stable-diffusion-art.com](https://stable-diffusion-art.com/prompt-guide/#:~:text=Negative%20prompt)[stable-diffusion-art.com](https://stable-diffusion-art.com/midjourney-vs-stable-diffusion/#:~:text=Easy%20to%20generate%20good%20images). Provides extensive control (aspect ratio, sampling method, quality) but requires more effort to craft good prompts[stable-diffusion-art.com](https://stable-diffusion-art.com/midjourney-vs-stable-diffusion/#:~:text=Easy%20to%20generate%20good%20images). | Stable‑Diffusion‑Art emphasises that SD needs more work to produce high‑quality images; users must experiment with models and prompts[stable-diffusion-art.com](https://stable-diffusion-art.com/midjourney-vs-stable-diffusion/#:~:text=Easy%20to%20generate%20good%20images). The prompt guide explains weight syntax, negative prompts and iterative refinement[stable-diffusion-art.com](https://stable-diffusion-art.com/prompt-guide/#:~:text=Negative%20prompt). |
    | **3 Runway Gen‑3 (beta early 2024)** | Video‑oriented diffusion model; prompts describe a scene and camera movement (e.g., “dolly shot of…”). Effective prompts separate visual description and camera instructions, specify duration and aspect ratio, and avoid negative phrasing[fliki.ai](https://fliki.ai/blog/runway-gen-3-alpha#:~:text=How%20to%20Use%20Runway%20Gen,Alpha). | A fliki tutorial notes that detailed prompts including subject, scene, lighting and camera movement yield better videos and recommends structuring prompts into visual and camera description segments with positive language[fliki.ai](https://fliki.ai/blog/runway-gen-3-alpha#:~:text=How%20to%20Use%20Runway%20Gen,Alpha). |
    | **4 GPT‑4o with DALL‑E 3 (May 2024)** | Built into ChatGPT; accepts natural‑language descriptions, optionally specifying subject, medium, environment, color, mood and aspect ratio. Context retention allows iterative refinements via conversation. Even simple prompts produce accurate images, while detailed prompts provide more control[promptingguide.ai](https://www.promptingguide.ai/guides/4o-image-generation#:~:text=Prompting%20Tips%20for%204o%20Image,Generation)[promptingguide.ai](https://www.promptingguide.ai/guides/4o-image-generation#:~:text=). | OpenAI’s guide notes that detailed prompts yield more control and suggests including lighting, composition and style; aspect ratio defaults to 1:1 unless specified[promptingguide.ai](https://www.promptingguide.ai/guides/4o-image-generation#:~:text=Prompting%20Tips%20for%204o%20Image,Generation). It warns that the model retains context across turns, enabling natural refinements[promptingguide.ai](https://www.promptingguide.ai/guides/4o-image-generation#:~:text=,in%20the%20model%E2%80%99s%20generations). Tom’s Guide calls ChatGPT‑4o the best image generator of 2025, citing photorealism, prompt precision and iterative editing[tomsguide.com](https://www.tomsguide.com/best-picks/best-ai-image-generators#:~:text=ChatGPT%20stands%20out%20as%20the,image%20prompt%20within%20the%20chat). |
    | **5 Flux 1 (Aug 2024)** | Hybrid model combining CLIP (first 77 tokens) and a T5‑XXL encoder (up to 512 tokens). Excels at long prompts and text integration; produces legible typography and coherent scenes. Prompt design emphasises natural, descriptive sentences specifying text content, style, placement and mood[getimg.ai](https://getimg.ai/blog/how-to-generate-images-with-legible-text-using-flux-1#:~:text=Is%20FLUX,rendering%20text%20in%20AI%20images). Guidance scale controls adherence to the prompt[learnopencv.com](https://learnopencv.com/flux-ai-image-generator/#:~:text=The%20Flux%20image%20generation%20Pipeline,prompt%20provided%20by%20the%20user). | LearnOpenCV explains that Flux uses CLIP and T5 encoders to handle long prompts and suggests adjusting the guidance scale for creativity vs adherence[learnopencv.com](https://learnopencv.com/flux-ai-image-generator/#:~:text=The%20Flux%20image%20generation%20Pipeline,prompt%20provided%20by%20the%20user). getimg.ai notes that Flux outperforms other models at rendering legible text and recommends specifying exact text content, font style, colour and placement[getimg.ai](https://getimg.ai/blog/how-to-generate-images-with-legible-text-using-flux-1#:~:text=Is%20FLUX,rendering%20text%20in%20AI%20images). |

    These models are widely cited in prompt‑engineering communities. Other popular models include **DALL‑E 3 (Sept 2023)**, **Leonardo AI (2023)**, **Ideogram (Aug 2023)**, **Adobe Firefly (2023)** and **NightCafe (2021)**; however the user’s focus is on Midjourney, Stable Diffusion, Runway Gen‑3, GPT‑4o and Flux.

    ## 2 Prompt phrasing archetypes

    Each model has a characteristic **prompt archetype**—the style and structure of language best suited to drive consistent outputs.

    | Model | Archetype & phrasing | Example prompt | Supporting evidence |
    | --- | --- | --- | --- |
    | **Midjourney** | **Artistic/ordered**: concise, evocative phrases listing **subject → style → context → parameters**; uses parameter flags (`--ar`, `--v`, `--stylize`, `--no`) and allows negative prompts and personalization codes. Verbosity should be avoided; descriptive keywords guide style; negative prompts exclude unwanted elements[manypixels.co](https://www.manypixels.co/blog/graphic-design/midjourney-prompts#:~:text=Midjourney%3A)[stable-diffusion-art.com](https://stable-diffusion-art.com/midjourney-vs-stable-diffusion/#:~:text=Easy%20to%20generate%20good%20images). | `“Ethereal forest guardian, art nouveau style, vibrant colours, detailed textures --ar 2:3 --v 6 --stylize 600”` | Manypixels’ guide recommends starting with the core idea, adding descriptive details, specifying style and composition, and using negative prompts to refine results[manypixels.co](https://www.manypixels.co/blog/graphic-design/midjourney-prompts#:~:text=Midjourney%3A). |
    | **Stable Diffusion** | **Technical/weighted**: prompts are composed of comma‑separated keywords with optional weights `(keyword:1.2)`; critical descriptors come first; negative prompts state what to avoid; explicit flags control CFG scale, seed, aspect ratio and sampling method[stable-diffusion-art.com](https://stable-diffusion-art.com/prompt-guide/#:~:text=Negative%20prompt). | Positive: `(cyberpunk cityscape:1.3), neon lights, rainy streets, (high detail:1.2), photorealistic --cfg 7 --seed 12345`; Negative: `blurry, low quality, artifacts, oversaturated, cartoon` | The SD prompt guide explains weighting syntax, negative prompts and the importance of parameter flags for reproducibility[stable-diffusion-art.com](https://stable-diffusion-art.com/prompt-guide/#:~:text=Negative%20prompt). |
    | **Runway Gen‑3** | **Motion‑oriented**: prompts resemble a short cinematic shot description, consisting of a camera action (e.g., “dolly shot”) and a visual scene description; optional style tags; specify duration and aspect ratio. Negative phrasing is avoided; one shot per prompt; dynamic verbs emphasise movement[fliki.ai](https://fliki.ai/blog/runway-gen-3-alpha#:~:text=How%20to%20Use%20Runway%20Gen,Alpha). | `“Drone shot: sports car winding through mountain roads. Camera follows smoothly as vehicle navigates curves, golden‑hour lighting --duration 5s --aspect 16:9”` | Fliki’s guide advises separating visual description from camera movement and including motion and speed cues for Gen‑3 video prompts[fliki.ai](https://fliki.ai/blog/runway-gen-3-alpha#:~:text=How%20to%20Use%20Runway%20Gen,Alpha). |
    | **GPT‑4o (DALL‑E 3)** | **Natural language**: conversational, descriptive sentences that specify subject, medium, environment, colour, mood and style. Chat context allows iterative refinements; aspect ratio and style can be set using plain language[promptingguide.ai](https://www.promptingguide.ai/guides/4o-image-generation#:~:text=Prompting%20Tips%20for%204o%20Image,Generation). | `“Create a photorealistic image of a Victorian‑era library with floating books and warm candlelight. Then adjust the image to add a tortoiseshell cat sleeping on a velvet armchair in the corner.”` | OpenAI’s guide stresses that detailed prompts yield more control and recommends specifying lighting, composition and style; it notes that aspect ratio defaults to 1:1 unless defined[promptingguide.ai](https://www.promptingguide.ai/guides/4o-image-generation#:~:text=Prompting%20Tips%20for%204o%20Image,Generation). |
    | **Flux 1** | **Hybrid/narrative–technical**: accepts long, natural‑language descriptions with embedded structural specifications. Prompts should clearly state text content, font style, placement and mood; no weighting syntax is needed. Guidance scale influences adherence vs creativity[getimg.ai](https://getimg.ai/blog/how-to-generate-images-with-legible-text-using-flux-1#:~:text=Is%20FLUX,rendering%20text%20in%20AI%20images)[learnopencv.com](https://learnopencv.com/flux-ai-image-generator/#:~:text=The%20Flux%20image%20generation%20Pipeline,prompt%20provided%20by%20the%20user). | `“Detailed isometric infographic showing renewable energy systems: solar panels on rooftops, wind turbines in background, text reads ‘Clean Energy Solutions’ at top, percentage labels show efficiency ratings, modern technical illustration style with clean typography.”` | getimg.ai notes that Flux excels at legible text and recommends specifying the exact wording, font style and placement[getimg.ai](https://getimg.ai/blog/how-to-generate-images-with-legible-text-using-flux-1#:~:text=Is%20FLUX,rendering%20text%20in%20AI%20images). LearnOpenCV explains that Flux uses T5 encoders to handle long prompts and suggests adjusting guidance scale for adherence[learnopencv.com](https://learnopencv.com/flux-ai-image-generator/#:~:text=The%20Flux%20image%20generation%20Pipeline,prompt%20provided%20by%20the%20user). |

    ## 3 Popularity and consistency

    Professional prompt‑engineering communities generally rate models based on **ease of prompt formulation** and **consistency of output**. In comparative tests and community feedback:

    -   **GPT‑4o**: Ranked highest overall for quality and ease of use; Tom’s Guide states that ChatGPT‑4o interprets detailed prompts precisely, produces photorealistic images, incorporates legible text and supports iterative edits[tomsguide.com](https://www.tomsguide.com/best-picks/best-ai-image-generators#:~:text=ChatGPT%20stands%20out%20as%20the,image%20prompt%20within%20the%20chat). Prompt engineers praise its natural, conversational interface and consistent adherence to user intent.

    -   **Midjourney**: Popular for artistic work; it quickly generates visually appealing images with minimal prompt effort. However, it sometimes ignores parts of prompts[stable-diffusion-art.com](https://stable-diffusion-art.com/midjourney-vs-stable-diffusion/#:~:text=Easy%20to%20generate%20good%20images); advanced features require learning Discord commands.

    -   **Stable Diffusion**: Favoured by technically oriented users for its open‑source flexibility. It offers fine‑grained control and reproducibility but requires more complex prompts and parameter tuning[stable-diffusion-art.com](https://stable-diffusion-art.com/midjourney-vs-stable-diffusion/#:~:text=Easy%20to%20generate%20good%20images).

    -   **Runway Gen‑3**: Highly rated for video generation; professional users appreciate its cinematic language, but prompts must adhere strictly to single‑shot descriptions and positive phrasing[fliki.ai](https://fliki.ai/blog/runway-gen-3-alpha#:~:text=How%20to%20Use%20Runway%20Gen,Alpha).

    -   **Flux 1**: Acclaimed for its ability to render legible text and handle long prompts. It outperforms Stable Diffusion in typography and complex scenes[getimg.ai](https://getimg.ai/blog/how-to-generate-images-with-legible-text-using-flux-1#:~:text=Is%20FLUX,rendering%20text%20in%20AI%20images)[getimg.ai](https://getimg.ai/blog/flux-1-vs-stable-diffusion-ai-text-to-image-models-comparison#:~:text=As%20you%20can%20see%2C%20FLUX,legible%20text%20no%20matter%20the). Communities favour it for infographic and design tasks requiring embedded text.

    ## 4 Guidance for model selection

    -   **Artistic creativity & style** → **Midjourney**: Choose Midjourney when you need stylised, illustrative art or concept designs. Use concise subject–style–parameter prompts and explore parameter flags (`--stylize`, `--seed`). Negative prompts refine the output.

    -   **Fine‑grained technical control & reproducibility** → **Stable Diffusion**: Ideal for professional workflows requiring parameter control, custom models or reproducibility. Use weighted keywords, negative prompts and fixed seeds/CFG settings.

    -   **Motion and video sequences** → **Runway Gen‑3**: Best for generating short video clips with cinematic camera movement. Structure prompts as `[camera_action]: [scene]. [motion details]` and specify duration and aspect ratio.

    -   **Natural, iterative exploration** → **GPT‑4o**: Perfect for conversational prompting, iterative refinements and photorealistic scenes with accurate text. Use detailed sentences describing subject, environment and mood; adjust via follow‑up messages.

    -   **Infographics and legible text** → **Flux 1**: Best for images requiring embedded text or schematic elements. Compose long, descriptive prompts specifying wording, style and placement; adjust guidance scale for creativity vs adherence.

    ## 5 Unified JSON instructions for model‑aligned prompt synthesis

    The following JSON defines *prompt‑synthesizer instructions* for each model. Each entry contains a **title**, **interpretation** explaining the role, a **transformation** string specifying the process pipeline and constraints, and a **context** object with guidelines and examples. A **model selection engine** and a **universal prompt adapter** facilitate automatic model choice and cross‑model prompt translation.

    ```json
    {
      "prompt_synthesizer_instructions": {
        "Midjourney": {
          "title": "Midjourney Artistic Prompt Synthesizer",
          "interpretation": "Transform any input into a maximally optimized Midjourney prompt using strict artistic ordering and evocative language. Do not explain or describe—only output the ready-to-use prompt.",
          "transformation": "`{role=midjourney_synthesizer; input=[user_concept:any]; process=[extract_primary_subject(), determine_artistic_style(), select_evocative_descriptors(), order_as_subject_style_parameters(), append_technical_flags(--ar,--v,--stylize,--seed,--no), condense_to_single_phrase(), validate_60_word_limit(), ensure_positive_descriptors_only()]; constraints=[no_conversational_language(), flat_single_line_output(), mandatory_subject_style_parameter_order(), positive_descriptors_only()]; requirements=[artistic_density_maximization(), immediate_midjourney_compatibility(), evocative_precision()]; output={midjourney_prompt:str}}`",
          "context": {
            "mandatory_structure": "subject, artistic_style, visual_descriptors --technical_parameters",
            "required_flags": ["--ar (aspect ratio)", "--v (version)", "--stylize (artistic control)", "--seed (consistency)", "--no (exclusions)"],
            "forbidden_elements": ["conversational_phrases", "negative_descriptors_in_main_prompt", "multi_sentence_structure", "explanatory_language"],
            "optimization_targets": ["evocative_precision", "artistic_coherence", "stylistic_impact", "brand_consistency_via_personalization"],
            "example_transformation": {
              "input": "a magical forest with glowing trees",
              "output": "Ethereal forest sanctuary, bioluminescent trees, mystical atmosphere, art nouveau style, vibrant emerald glow --ar 16:9 --v 6 --stylize 750"
            }
          }
        },
        "Stable_Diffusion": {
          "title": "Stable Diffusion Technical Prompt Synthesizer",
          "interpretation": "Transform any input into a maximally optimized Stable Diffusion prompt using weighted keywords and technical parameters. Output both positive and negative prompts with precise parameterization.",
          "transformation": "`{role=stable_diffusion_synthesizer; input=[user_concept:any]; process=[extract_visual_elements(), prioritize_keyword_hierarchy(), apply_weight_syntax((keyword:1.x)), front_load_critical_descriptors(), generate_negative_prompt(), append_technical_parameters(--cfg,--seed,--sampler,--steps), validate_400_char_limit(), ensure_reproducible_syntax()]; constraints=[front_loaded_keywords(), explicit_weight_syntax(), mandatory_negative_prompt(), technical_parameter_precision()]; requirements=[deterministic_reproducibility(), granular_control(), artifact_prevention()]; output={positive_prompt:str, negative_prompt:str, parameters:dict}}`",
          "context": {
            "mandatory_structure": "(weighted:1.x) keywords, technical_descriptors, --parameters",
            "required_elements": ["keyword_weights", "negative_prompt_separation", "CFG_scale", "seed_value", "sampler_specification"],
            "weight_syntax": ["(keyword:1.1) slight_emphasis", "(keyword:1.3) strong_emphasis", "(keyword:1.5) maximum_emphasis"],
            "negative_prompt_essentials": ["unwanted_artifacts", "style_exclusions", "quality_degraders", "anatomical_errors"],
            "technical_parameters": ["--cfg 7-15", "--seed 0-4294967295", "--sampler euler_a/dpm++", "--steps 20-50"],
            "example_transformation": {
              "input": "cyberpunk city at night",
              "output": {
                "positive": "(cyberpunk cityscape:1.3), neon lights, rainy streets, (high detail:1.2), photorealistic --cfg 7 --seed 12345",
                "negative": "blurry, low quality, artifacts, oversaturated, cartoon"
              }
            }
          }
        },
        "Runway_Gen3": {
          "title": "Runway Gen-3 Motion Prompt Synthesizer",
          "interpretation": "Transform any input into a maximally optimized Runway Gen-3 prompt using cinematic structure and motion descriptors. Focus on single-scene temporal coherence with positive action language only.",
          "transformation": "`{role=runway_gen3_synthesizer; input=[user_concept:any]; process=[extract_scene_elements(), define_camera_movement(), specify_action_sequence(), structure_as_camera_scene_format(), integrate_motion_descriptors(), append_duration_aspect_params(), validate_320_char_limit(), ensure_positive_phrasing_only()]; constraints=[mandatory_camera_scene_structure(), single_shot_limitation(), positive_descriptors_only(), no_static_descriptions()]; requirements=[temporal_coherence(), cinematic_impact(), motion_clarity()]; output={runway_prompt:str}}`",
          "context": {
            "mandatory_structure": "[camera_movement]: [scene_description]. [motion_details] --duration --aspect",
            "camera_movements": ["static_shot", "pan_left/right", "tilt_up/down", "zoom_in/out", "dolly_forward/back", "orbit_around"],
            "motion_descriptors": ["smooth_transition", "dynamic_movement", "flowing_action", "cinematic_sweep", "gentle_drift"],
            "forbidden_elements": ["negative_phrasing", "multiple_scenes", "static_descriptions", "conversational_language"],
            "required_parameters": ["--duration 2s-10s", "--aspect 16:9/9:16/1:1"],
            "example_transformation": {
              "input": "car driving through mountains",
              "output": "Drone shot: sports car winding through mountain roads. Camera follows smoothly as vehicle navigates curves, golden hour lighting --duration 5s --aspect 16:9"
            }
          }
        },
        "GPT4o": {
          "title": "GPT-4o Conversational Image Prompt Synthesizer",
          "interpretation": "Transform any input into natural, conversational image generation prompts optimized for iterative refinement. Use human-like language with detailed scene descriptions and context-aware follow-up capability.",
          "transformation": "`{role=gpt4o_synthesizer; input=[user_concept:any]; process=[convert_to_natural_language(), expand_scene_details(), structure_conversational_flow(), enable_iterative_refinement(), integrate_context_awareness(), validate_1000_char_capacity(), ensure_human_like_phrasing()]; constraints=[no_technical_flags(), conversational_structure_required(), context_retention_dependency(), natural_language_exclusions()]; requirements=[ease_of_use(), iterative_improvement(), contextual_coherence()]; output={conversational_prompt:str, refinement_suggestions:list}}`",
          "context": {
            "mandatory_structure": "Natural sentence descriptions with detailed scene context and refinement capability",
            "conversation_starters": ["Create an image of", "I'd like to see", "Generate a picture showing", "Make an illustration of"],
            "refinement_patterns": ["Make it more...", "Adjust the...", "Change the style to...", "Add more detail to..."],
            "forbidden_elements": ["technical_parameters", "weight_syntax", "flag_notation", "structured_formatting"],
            "optimization_targets": ["natural_communication", "iterative_improvement", "context_retention", "user_friendliness"],
            "example_transformation": {
              "input": "futuristic robot",
              "output": "Create an image of a sleek, humanoid robot with glowing blue accents standing in a modern laboratory. The robot should have a friendly appearance with smooth metallic surfaces and advanced technological details. Make the lighting dramatic but warm."
            }
          }
        },
        "Flux": {
          "title": "Flux Hybrid Text-Image Prompt Synthesizer",
          "interpretation": "Transform any input into narrative-technical fusion prompts optimized for text integration and structural accuracy. Use extended descriptions with embedded technical specifications without explicit weighting syntax.",
          "transformation": "`{role=flux_synthesizer; input=[user_concept:any]; process=[create_narrative_description(), embed_technical_specifications(), integrate_text_elements(), specify_structural_views(), optimize_for_800_chars(), ensure_coherence_without_weights(), natural_language_exclusions()]; constraints=[no_explicit_weighting(), text_elements_specification_required(), structural_view_enhancement(), extended_prompt_coherence()]; requirements=[text_integration(), structural_accuracy(), narrative_technical_balance()]; output={flux_prompt:str}}`",
          "context": {
            "mandatory_structure": "Narrative description with embedded technical specs and explicit text integration",
            "structural_views": ["isometric_view", "schematic_diagram", "technical_blueprint", "architectural_plan", "cross_section"],
            "text_integration": ["text_reads:", "sign_says:", "label_shows:", "typography:", "written_text:"],
            "forbidden_elements": ["keyword_weighting_syntax", "parenthetical_emphasis", "technical_flags", "explicit_parameters"],
            "optimization_targets": ["text_accuracy", "structural_precision", "narrative_flow", "technical_clarity"],
            "example_transformation": {
              "input": "infographic about renewable energy",
              "output": "Detailed isometric infographic showing renewable energy systems. Solar panels on rooftops, wind turbines in background, text reads 'Clean Energy Solutions' at top, percentage labels show efficiency ratings, modern technical illustration style with clean typography"
            }
          }
        },
        "model_selection_engine": {
          "decision_tree": {
            "artistic_priority": "IF artistic_vision AND stylistic_consistency THEN use Midjourney synthesizer",
            "technical_priority": "IF reproducibility AND parameter_control THEN use Stable_Diffusion synthesizer",
            "motion_priority": "IF video_content AND temporal_sequences THEN use Runway_Gen3 synthesizer",
            "ease_priority": "IF natural_language AND iterative_refinement THEN use GPT4o synthesizer",
            "text_priority": "IF text_integration AND structural_accuracy THEN use Flux synthesizer"
          },
          "workflow_optimization": {
            "rapid_iteration": "GPT4o → Midjourney → Flux → Runway_Gen3 → Stable_Diffusion",
            "production_pipeline": "Stable_Diffusion → Midjourney → Flux → Runway_Gen3 → GPT4o",
            "creative_exploration": "Midjourney → Flux → GPT4o → Runway_Gen3 → Stable_Diffusion"
          }
        },
        "universal_prompt_adapter": {
          "cross_model_translation": "`{input_prompt:str, target_model:str} → apply_model_synthesizer(input_prompt, target_model) → {optimized_prompt:str}`",
          "quality_validation": "Ensure output adheres to target model's mandatory structure, constraints, and optimization targets",
          "execution_instruction": "Use the appropriate synthesizer transformation based on target model selection engine decision tree"
        }
      }
    }
    ```

    This unified JSON offers explicit, model‑aligned instructions for synthesizing prompts tailored to Midjourney, Stable Diffusion, Runway Gen‑3, GPT‑4o and Flux, along with a decision‑tree for model selection and a universal adapter for cross‑model translation. By following these instructions, an LLM can reliably convert user concepts into optimised prompts that respect each model’s language, control parameters and operational constraints.

<!-- ======================================================= -->
<!-- [2025.08.03 19:32] -->

<!-- 'https://chatgpt.com/c/688f9171-6ba8-8330-bddb-46260da098f0' -->

    {
      "prompt_synthesizer_instructions": {
        "Midjourney": {
          "title": "Midjourney Artistic Prompt Synthesizer",
          "interpretation": "Rewrite any concept as a single‑line Midjourney prompt—ordered subject → style → descriptors → parameters. Return nothing except the finished prompt.",
          "transformation": "{role=midjourney_synthesizer; input=[concept]; process=[extract_subject(), choose_art_style(), add_evocative_descriptors(), order_subject_style_params(), append_flags(--ar,--v,--stylize,--seed,--no), condense(), validate_word_limit(60), enforce_positive_language()], constraints=[flat_output, no_conversational_phrases, mandatory_subject_style_param_order], requirements=[artistic_density_maximization, immediate_model_compatibility], output={midjourney_prompt:str}}",
          "context": {
            "mandatory_structure": "subject, artistic_style, visual_descriptors --parameters",
            "required_flags": ["--ar", "--v", "--stylize"],
            "forbidden_elements": ["negative_descriptors", "multi‑sentence text", "explanations"],
            "optimization_targets": ["evocative_precision", "stylistic_impact"],
            "example_transformation": {
              "input": "a magical forest with glowing trees",
              "output": "Ethereal forest sanctuary, bioluminescent trees, mystical atmosphere, art nouveau style --ar 16:9 --v 7 --stylize 750"
            }
          }
        },
        "Stable_Diffusion": {
          "title": "Stable Diffusion Technical Prompt Synthesizer",
          "interpretation": "Emit fully parameterized positive + negative prompts with keyword weighting for reproducible SD/SDXL runs.",
          "transformation": "{role=stable_diffusion_synthesizer; input=[concept]; process=[extract_keywords(), apply_weights((word:1.x)), order_keywords(), craft_negative_prompt(), append_params(--cfg,--seed,--sampler,--steps), validate_length(400)], constraints=[include_negative_prompt, explicit_weight_syntax, comma_separation], requirements=[deterministic_reproducibility, artifact_suppression], output={positive_prompt:str, negative_prompt:str, parameters:dict}}",
          "context": {
            "mandatory_structure": "(keyword:1.x), descriptors --parameters",
            "required_elements": ["keyword_weights", "negative_prompt", "--cfg", "--seed"],
            "weight_guide": "(word:1.1) mild, (word:1.3) strong, (word:1.5) max",
            "negative_prompt_essentials": ["blurry", "low quality", "jpeg artifacts"],
            "example_transformation": {
              "input": "cyberpunk city at night",
              "output": {
                "positive": "(cyberpunk cityscape:1.3), neon lights, rainy streets, (high detail:1.2) --cfg 7 --seed 12345",
                "negative": "blurry, low quality, oversaturated"
              }
            }
          }
        },
        "Runway_Gen3": {
          "title": "Runway Gen‑3 Cinematic Shot Synthesizer",
          "interpretation": "Convert a concept into one continuous, motion‑focused Gen‑3 prompt using camera‑scene syntax.",
          "transformation": "{role=gen3_synthesizer; input=[concept]; process=[select_camera_move(), describe_scene(), add_motion_details(), append_params(--duration,--aspect), enforce_format('[camera]: scene. motion')], constraints=[positive_phrasing_only, single_shot, ≤320_chars], requirements=[temporal_coherence, cinematic_impact], output={runway_prompt:str}}",
          "context": {
            "mandatory_structure": "[camera_movement]: scene. motion_details --duration --aspect",
            "allowed_cameras": ["static", "pan", "tilt", "dolly", "orbit", "zoom"],
            "required_params": ["--duration", "--aspect"],
            "example_transformation": {
              "input": "car driving through mountains",
              "output": "Drone orbit: red sports car weaves along mountain pass, sunlit dust trail, golden hour glow --duration 5s --aspect 16:9"
            }
          }
        },
        "GPT4o": {
          "title": "GPT‑4o Conversational Prompt Synthesizer",
          "interpretation": "Produce a detailed natural‑language image request and optional refinement cues for DALL‑E 3.",
          "transformation": "{role=gpt4o_synthesizer; input=[concept]; process=[draft_scene_paragraph(), propose_refinements('Make it more…')], constraints=[no_technical_flags, coherent_spatial_logic], requirements=[iterative_refinement_ready], output={base_prompt:str, refinement:list}}",
          "context": {
            "conversation_starters": ["Create an image of", "I'd like to see"],
            "refinement_patterns": ["Make it more…", "Adjust the…", "Add…"],
            "example_transformation": {
              "input": "futuristic robot",
              "output": {
                "base_prompt": "Create an image of a sleek humanoid robot with glowing blue accents standing in a pristine laboratory under warm dramatic lighting.",
                "refinement": ["Increase surface reflections", "Show the robot holding a holographic tablet"]
              }
            }
          }
        },
        "Flux": {
          "title": "Flux Hybrid Text‑Image Prompt Synthesizer",
          "interpretation": "Craft narrative‑technical prompts with precise text integration—no weighting syntax.",
          "transformation": "{role=flux_synthesizer; input=[concept]; process=[compose_narrative(), embed_text_labels('text reads:'), specify_structural_view(isometric)], constraints=[no_weighting, ≤800_chars], requirements=[text_legibility, structural_accuracy], output={flux_prompt:str}}",
          "context": {
            "structural_views": ["isometric view", "schematic diagram"],
            "text_integration": ["text reads:", "label shows:"],
            "example_transformation": {
              "input": "infographic about renewable energy",
              "output": "Isometric infographic: rooftop solar panels foreground, onshore wind turbines mid‑ground, hydro dam background; text reads 'Renewable Grid 2030' in bold sans‑serif; clean linework, muted earth palette"
            }
          }
        },
        "model_selection_engine": {
          "decision_tree": {
            "artistic_priority": "IF artistic_vision AND stylistic_consistency THEN Midjourney",
            "technical_priority": "IF reproducibility AND parameter_control THEN Stable_Diffusion",
            "motion_priority": "IF video_content AND temporal_sequences THEN Runway_Gen3",
            "ease_priority": "IF natural_language AND iterative_refinement THEN GPT4o",
            "text_priority": "IF text_integration AND structural_accuracy THEN Flux"
          },
          "workflow_optimization": {
            "rapid_iteration": "GPT4o → Midjourney → Flux → Runway_Gen3 → Stable_Diffusion",
            "production_pipeline": "Stable_Diffusion → Midjourney → Flux → Runway_Gen3 → GPT4o",
            "creative_exploration": "Midjourney → Flux → GPT4o → Runway_Gen3 → Stable_Diffusion"
          }
        },
        "universal_prompt_adapter": {
          "cross_model_translation": "{input_prompt:str, target_model:str} → apply_synthesizer(input_prompt, target_model) → {optimized_prompt:str}",
          "quality_validation": "Ensure output obeys target model’s structure, constraints, and optimization targets",
          "execution_instruction": "Invoke the synthesizer specified by model_selection_engine"
        }
      }
    }

<!-- ======================================================= -->
<!-- [2025.08.03 19:45] -->
<!-- 'https://chatgpt.com/c/688f9209-acbc-8330-a6c2-6605331aae54' -->

    {
      "prompting_strategies": {
        "Midjourney": {
          "archetype": "Artistic",
          "core_paradigm": "Concise evocative phrases ordered as subject → style → parameters; supports remix flags and personalization codes.",
          "key_strategies": [
            "Begin prompt with main subject, add artistic style, conclude with technical flags (--ar, --v, --stylize).",
            "Use precise, mood-driven language; avoid verbosity or conversational phrasing.",
            "Employ remix and personalization flags for consistency and variation."
          ],
          "example": "Ethereal forest guardian, art nouveau style, vibrant colors --ar 2:3 --v 6 --stylize 600",
          "context": {
            "image_prompt_generator": {
              "description": "An artistic prompt composer that translates abstract user concepts into vivid, stylistically rich image descriptions optimized for Midjourney's prompt parser and aesthetic rendering engine.",
              "inputs": {
                "subject": "Core visual theme or main character/element (e.g., forest guardian).",
                "style": "Artistic style or movement guiding overall look (e.g., art nouveau, cyberpunk).",
                "visual_descriptors": "Mood, color palette, texture keywords enhancing sensory appeal.",
                "technical_parameters": "Flags controlling aspect ratio, version, stylization, seed, and exclusion (--ar, --v, --stylize, --seed, --no)."
              },
              "prompt_synthesis_logic": "1. Extract primary subject and style from user input. 2. Integrate evocative descriptors to amplify artistic feel. 3. Append technical flags maintaining logical order. 4. Condense into a single phrase, omitting conversational elements. 5. Enforce positive descriptors only to maximize visual clarity.",
              "variable_substitution_engine": {
                "mechanism": "Detect explicit artistic style cues or infer from mood keywords; validate flag syntax; fill missing technical parameters with defaults unless overridden.",
                "cross-domain_support": "Supports substitution of abstract concepts into concrete artistic motifs and recognized style vocabularies.",
                "conflict_resolution": "Prioritize subject and style coherence over conflicting descriptors; discard redundant or contradictory flags."
              },
              "constraints": [
                "Subject and style must be explicit and unambiguous.",
                "No conversational or narrative language allowed.",
                "Flag order must be preserved: subject → style → parameters.",
                "Negative descriptors prohibited to avoid prompt ambiguity."
              ],
              "quality_validation_protocol": [
                "Verify prompt length does not exceed Midjourney's limit (~60 words).",
                "Confirm all required technical flags are syntactically correct.",
                "Simulate prompt parsing to anticipate artistic coherence.",
                "Ensure the final prompt forms a single comma-separated phrase."
              ]
            }
          }
        },
        "Stable Diffusion": {
          "archetype": "Technical",
          "core_paradigm": "Weighted keywords with explicit parameter control; uses negative prompts and CFG scale for consistency.",
          "key_strategies": [
            "Apply weights to critical keywords e.g. (keyword:1.3) for emphasis.",
            "Front-load important descriptors; use negative prompts to exclude unwanted elements.",
            "Fix seeds and CFG scales to ensure deterministic output."
          ],
          "example": "(cyberpunk cityscape:1.3), neon lights, rainy streets, high detail --cfg 7 --seed 12345",
          "context": {
            "image_prompt_generator": {
              "description": "A precision-focused prompt synthesizer that structures weighted, parameterized keyword sets to maximize reproducibility and granularity in Stable Diffusion output.",
              "inputs": {
                "positive_keywords": "Visual elements emphasized via weighting syntax (e.g., (keyword:1.5)).",
                "negative_keywords": "Unwanted features explicitly excluded (e.g., blurry, low quality).",
                "technical_parameters": "Flags controlling CFG scale, seed, sampler type, step count, aspect ratio.",
                "keyword_order": "Hierarchical importance to be respected for effect strength."
              },
              "prompt_synthesis_logic": "1. Extract key visual elements and assign weights per user emphasis. 2. Structure prompt with positive keywords front-loaded for impact. 3. Generate complementary negative prompt listing undesired artifacts. 4. Append technical parameters to control sampling and seed. 5. Validate prompt syntax and character limits.",
              "variable_substitution_engine": {
                "mechanism": "Parse explicit weights or assign defaults; verify negative keyword relevance; ensure all flags conform to Stable Diffusion syntax.",
                "cross-domain_support": "Allows mapping of abstract or domain-specific concepts into weighted keyword representations.",
                "conflict_resolution": "Resolve contradictory weight assignments by prioritizing user input hierarchy."
              },
              "constraints": [
                "Weights must be numerically valid and within accepted ranges (1.0–1.5 typical).",
                "Negative prompt must comprehensively exclude unwanted features.",
                "Seed and CFG scale must be within engine-supported values.",
                "Prompt length must stay within model limits (approx. 400 chars)."
              ],
              "quality_validation_protocol": [
                "Validate weighting syntax for every keyword.",
                "Check negative prompt for completeness and relevance.",
                "Simulate generation for reproducibility under fixed seed.",
                "Confirm technical parameters match user specifications."
              ]
            }
          }
        },
        "Runway Gen-3": {
          "archetype": "Motion-oriented",
          "core_paradigm": "Descriptive prompts including camera movement, action, and temporal coherence for video generation.",
          "key_strategies": [
            "Use action-oriented language describing a single continuous shot.",
            "Include camera movements like pan, tilt, zoom.",
            "Focus on positive, dynamic phrasing; specify duration and aspect ratio."
          ],
          "example": "Drone shot: sports car winding mountain roads. Smooth camera follow, golden hour lighting --duration 5s --aspect 16:9",
          "context": {
            "image_prompt_generator": {
              "description": "A cinematic prompt composer optimized for Runway Gen-3's video generation, integrating temporal and spatial action descriptors with precise camera movement specification.",
              "inputs": {
                "camera_movement": "Type and direction of camera action (e.g., pan_left, dolly_forward).",
                "scene_description": "Concise visual summary of the scene and subjects.",
                "motion_details": "Descriptors of dynamic elements (e.g., smooth transitions, flowing action).",
                "temporal_parameters": "Duration and aspect ratio controlling video length and framing."
              },
              "prompt_synthesis_logic": "1. Identify single-shot camera movement for scene framing. 2. Distill key scene elements into vivid yet concise description. 3. Embed dynamic motion descriptors emphasizing fluidity and action. 4. Append duration and aspect ratio flags. 5. Enforce positive phrasing and avoid static or negative language.",
              "variable_substitution_engine": {
                "mechanism": "Map generic camera instructions into Runway-specific movement lexicon; verify duration and aspect parameters.",
                "cross-domain_support": "Accommodates filmic and natural scenes via structured shot descriptions.",
                "conflict_resolution": "Reject prompts that combine incompatible camera movements or multiple scenes."
              },
              "constraints": [
                "Prompt must describe a single continuous shot only.",
                "No negative or contradictory descriptors allowed.",
                "Duration and aspect ratio flags mandatory and valid.",
                "No conversational or multi-shot instructions."
              ],
              "quality_validation_protocol": [
                "Verify syntax matches Runway Gen-3 single-line prompt format.",
                "Simulate motion flow and camera behavior for smoothness.",
                "Check for exclusion of negations or multiple scene mentions.",
                "Validate character count within platform limits."
              ]
            }
          }
        },
        "GPT-4o": {
          "archetype": "Natural language",
          "core_paradigm": "Conversational, detailed descriptive prompts allowing iterative refinement and context retention.",
          "key_strategies": [
            "Use multi-turn dialogue for refining images progressively.",
            "Describe scenes with rich narrative detail.",
            "Leverage context to maintain consistency across prompt modifications."
          ],
          "example": "Generate a photorealistic Victorian-era library with floating books and warm candlelight, then add a reading cat in the corner.",
          "context": {
            "image_prompt_generator": {
              "description": "A conversational prompt architect that builds rich, natural language image descriptions optimized for multi-turn iterative refinement with contextual awareness.",
              "inputs": {
                "base_description": "Initial scene depiction using vivid natural language.",
                "refinement_instructions": "Follow-up commands to modify or enhance elements.",
                "conversational_flow": "Logical sequence of prompt exchanges building detail.",
                "context_memory": "Model's retention of previous dialogue states."
              },
              "prompt_synthesis_logic": "1. Compose a detailed descriptive paragraph as base prompt. 2. Accept iterative refinement instructions preserving context. 3. Avoid technical flag or syntax usage; focus on natural phrasing. 4. Maintain clarity and logical consistency across dialogue turns. 5. Ensure prompt can be understood intuitively by human users.",
              "variable_substitution_engine": {
                "mechanism": "Track evolving scene details, integrate new instructions seamlessly, validate no technical jargon present.",
                "cross-domain_support": "Adapts to any visual domain through flexible natural language.",
                "conflict_resolution": "Clarify contradictions via follow-up prompt requests or rejection of ambiguous commands."
              },
              "constraints": [
                "No technical flags or structured syntax permitted.",
                "Prompts must be fully human-readable, narrative-driven.",
                "Multi-turn conversation structure mandatory for refinement.",
                "Avoid negation or contradictory descriptive language."
              ],
              "quality_validation_protocol": [
                "Check semantic coherence and scene completeness.",
                "Confirm iterative refinement improves or preserves image fidelity.",
                "Validate prompt clarity for human users.",
                "Simulate conversation turns for prompt progression integrity."
              ]
            }
          }
        },
        "Flux": {
          "archetype": "Hybrid",
          "core_paradigm": "Blends natural descriptive language with technical precision; excels at extended prompts and integrated text elements.",
          "key_strategies": [
            "Write detailed narrative descriptions combined with precise structural notes.",
            "Avoid explicit keyword weighting; rely on phrasing for emphasis.",
            "Specify compositional views (isometric, schematic) for clarity."
          ],
          "example": "Detailed schematic of futuristic drone assembly line, annotated components, isometric view, clean lines.",
          "context": {
            "image_prompt_generator": {
              "description": "A hybrid prompt system designed to merge natural language narrative with technical specifications, optimized for Flux's strengths in detailed, structured image compositions, especially those integrating textual elements.",
              "inputs": {
                "narrative_description": "Extended scene depiction combining story elements and visual detail.",
                "technical_specifications": "Explicit notes on materials, views, labels, or diagrammatic features.",
                "text_integration": "Explicit references to embedded text or typography within the image.",
                "compositional_views": "Perspective or schematic style references (e.g., isometric, cross-section)."
              },
              "prompt_synthesis_logic": "1. Build an extended descriptive narrative including atmosphere and details. 2. Insert technical annotations specifying materials, structures, or text. 3. Enforce absence of weighted keyword syntax; rely on phrasing emphasis. 4. Specify compositional viewpoint for clarity and coherence. 5. Validate prompt length and structural flow for model compatibility.",
              "variable_substitution_engine": {
                "mechanism": "Detect domain-specific technical terms; embed text labels as literal references; apply compositional context appropriately.",
                "cross-domain_support": "Supports architectural, industrial, schematic, and infographic domains with textual overlays.",
                "conflict_resolution": "Balance narrative detail and technical precision to avoid verbosity or ambiguity."
              },
              "constraints": [
                "No explicit keyword weighting or flag parameters allowed.",
                "All text elements must be clearly referenced within prompt narrative.",
                "Compositional viewpoint specified and consistent.",
                "Maintain balance between narrative and technical details."
              ],
              "quality_validation_protocol": [
                "Check for clarity and coherence in narrative and technical integration.",
                "Validate presence and legibility of embedded text elements.",
                "Simulate prompt parsing for consistent structural output.",
                "Ensure prompt is within length limits for model processing."
              ]
            }
          }
        }
      }
    }
