<!-- ======================================================= -->
<!-- [2025.08.03 17:04] -->

<!-- 'https://www.perplexity.ai/search/please-list-the-10-most-popula-6VB3.KEGTqqA5x.rUCOa3g' -->

    {
      "Midjourney": {
        "archetype": "artistic",
        "core_strategy": "Phrase prompts as concise, evocative descriptions ordered by subject, style, and technical parameters. Use flags for aspect ratio, version, and stylization. Avoid excessive verbosity; prioritize the core elements you want to appear.",
        "prompt_example": "Ethereal forest guardian, art nouveau style, vibrant colors, detailed textures --ar 2:3 --v 6 --stylize 600",
        "recommended_elements": [
          "subject",
          "artistic style",
          "color/tone adjectives",
          "ordered descriptors",
          "Midjourney flags (--ar, --v, --stylize)"
        ]
      },
      "StableDiffusion": {
        "archetype": "technical",
        "core_strategy": "Construct prompts using weighted keywords for priority, technical controls (aspect ratio, quality, negative prompts), and explicit detail. Use parameter syntax for fine-tuning and reproducible outputs.",
        "prompt_example": "Cyberpunk cityscape at night, neon lights, rainy streets, high detail, (photorealistic:1.3), --ar 16:9 --v 5 --q 2",
        "recommended_elements": [
          "primary subject(s)",
          "weighted keywords (e.g., (term:weight))",
          "negative prompts",
          "parameters (--ar, --v, --q)",
          "descriptive technical detail"
        ]
      },
      "GPT-4o": {
        "archetype": "natural_language",
        "core_strategy": "Use full-sentence, conversational prompts with detailed descriptions. Chain modifications iteratively; direct the model with clear, human-language revisions without syntax or parameter flags.",
        "prompt_example": "Generate a photorealistic image of a Victorian-era library with floating books and warm candlelight, then adjust to add a reading cat in the corner.",
        "recommended_elements": [
          "scene context",
          "explicit objects or actions",
          "modifiers (color, mood, lighting)",
          "sequential instructions for refinements",
          "avoid technical keywords"
        ]
      },
      "Flux": {
        "archetype": "hybrid",
        "core_strategy": "Blend technical descriptors with natural, narrative elements. Focus on structural accuracy in extended prompts, particularly for layouts or schematic images. Avoid weighting but detail component relationships.",
        "prompt_example": "A detailed schematic of a futuristic drone assembly line, including annotated components and workflow arrows, in isometric view with clean lines.",
        "recommended_elements": [
          "complex subject or system",
          "component relationships",
          "narrative or instructional phrasing",
          "art style or perspective",
          "label or annotation specification"
        ]
      },
      "Runway Gen-3": {
        "archetype": "motion-oriented",
        "core_strategy": "Compose prompts to specify temporal actions, transitions, and cinematic style. Indicate time flow or animated sequences when relevant. Use available motion or duration parameters.",
        "prompt_example": "A serene mountain landscape transitioning from dawn to dusk, with flowing rivers and drifting clouds, in cinematic style --duration 5s --aspect 16:9",
        "recommended_elements": [
          "dynamic actions or transformations",
          "descriptive environment and motion cues",
          "specified duration or frames",
          "style settings (cinematic, photorealistic)",
          "aspect ratio or output flags"
        ]
      }
    }

<!-- ======================================================= -->
<!-- [2025.08.03 17:04] -->
<!-- gpt-4.1 -->

    [
      {
        "model": "Midjourney",
        "prompting_strategy": {
          "archetype": "artistic",
          "structure": "subject, style, context, parameters",
          "features": [
            "Concise, evocative phrases",
            "Ordered elements for clarity",
            "Use of parameters (e.g., --ar, --v, --stylize) for output control",
            "Remix and version flags for targeted modifications"
          ],
          "example": "Ethereal forest guardian, art nouveau style, vibrant colors, detailed textures --ar 2:3 --v 6 --stylize 600"
        }
      },
      {
        "model": "Stable Diffusion",
        "prompting_strategy": {
          "archetype": "technical",
          "structure": "weighted keywords, negative prompts, explicit parameters",
          "features": [
            "Parameter-rich inputs (weights, CFG scales)",
            "Explicit control over output style, aspect, detail",
            "Fine-tuning possible via negative prompts and seed fixation",
            "Photographic and art technical terms recommended"
          ],
          "example": "Cyberpunk cityscape at night, neon lights, rainy streets, high detail, (photorealistic:1.3), --ar 16:9 --v 5 --q 2"
        }
      },
      {
        "model": "Runway Gen-3",
        "prompting_strategy": {
          "archetype": "motion-oriented",
          "structure": "scene description, temporal/motion cues, style parameters",
          "features": [
            "Emphasizes dynamic scenes and transitions",
            "Temporal descriptors for sequential frame coherence",
            "Parameters for duration, aspect ratio, and cinematic style",
            "Optimized for both still and video prompt chains"
          ],
          "example": "A serene mountain landscape transitioning from dawn to dusk, with flowing rivers and drifting clouds, in cinematic style --duration 5s --aspect 16:9"
        }
      },
      {
        "model": "GPT-4o (DALL-E)",
        "prompting_strategy": {
          "archetype": "natural language",
          "structure": "descriptive sentence, chain-of-thought instructions",
          "features": [
            "Conversational phrasing, no technical syntax required",
            "Iterative improvement via follow-up instructions",
            "Handles style, context, and corrections naturally",
            "Supports long or short prompts, context retention"
          ],
          "example": "Generate a photorealistic image of a Victorian-era library with floating books and warm candlelight, then adjust to add a reading cat in the corner."
        }
      },
      {
        "model": "Flux",
        "prompting_strategy": {
          "archetype": "hybrid",
          "structure": "technical descriptors + natural language + schematic/textual details",
          "features": [
            "Blends technical precision with narrative description",
            "Excels at images with embedded text or complex diagrams",
            "No strict weighting syntax but supports extended prompts",
            "Best for info-dense, instructional, or schematic outputs"
          ],
          "example": "A detailed schematic of a futuristic drone assembly line, including annotated components and workflow arrows, in isometric view with clean lines."
        }
      }
    ]


<!-- ======================================================= -->
<!-- [2025.08.03 17:05] -->
<!-- sonnet -->

    {
      "image_generation_prompting_strategies": {
        "gpt_4o": {
          "archetype": "natural_language",
          "strategy_profile": {
            "approach": "conversational_descriptive",
            "syntax_requirements": "minimal",
            "refinement_method": "iterative_chain_prompts",
            "consistency_mechanism": "context_retention"
          },
          "composition_structure": {
            "primary_descriptor": "detailed_natural_description",
            "modification_method": "follow_up_conversational_instructions",
            "parameter_format": "embedded_within_natural_language",
            "example_template": "[Detailed scene description], then [modification instruction]"
          },
          "optimal_patterns": {
            "length": "medium_to_long_descriptive",
            "tone": "conversational_instructional",
            "technical_specificity": "low",
            "iteration_support": "high"
          },
          "example_composition": {
            "initial_prompt": "Generate a photorealistic image of a Victorian-era library with floating books and warm candlelight",
            "refinement_prompt": "then adjust to add a reading cat in the corner"
          }
        },
        "stable_diffusion": {
          "archetype": "technical_parameterized",
          "strategy_profile": {
            "approach": "weighted_keyword_driven",
            "syntax_requirements": "high_technical_knowledge",
            "refinement_method": "parameter_adjustment_and_negative_prompts",
            "consistency_mechanism": "seed_fixing_and_cfg_scaling"
          },
          "composition_structure": {
            "primary_descriptor": "weighted_keywords",
            "parameter_format": "(keyword:weight_value)",
            "technical_controls": "--ar --v --q --seed",
            "negative_prompt_support": "explicit_exclusion_terms"
          },
          "optimal_patterns": {
            "length": "medium_with_technical_parameters",
            "tone": "technical_descriptive",
            "technical_specificity": "very_high",
            "iteration_support": "parameter_based"
          },
          "example_composition": {
            "structure": "[Main subject], [style descriptors], [technical quality terms], (weighted:1.3), [parameters]",
            "sample": "Cyberpunk cityscape at night, neon lights, rainy streets, high detail, (photorealistic:1.3), --ar 16:9 --v 5 --q 2"
          }
        },
        "midjourney": {
          "archetype": "artistic_structured",
          "strategy_profile": {
            "approach": "concise_evocative_ordered",
            "syntax_requirements": "moderate_artistic_knowledge",
            "refinement_method": "parameter_flags_and_remix",
            "consistency_mechanism": "stylization_codes_and_personalization"
          },
          "composition_structure": {
            "primary_descriptor": "subject_first",
            "style_placement": "after_subject",
            "parameter_format": "--parameter value",
            "ordering": "subject → style → context → technical_parameters"
          },
          "optimal_patterns": {
            "length": "short_to_medium_concise",
            "tone": "artistic_evocative",
            "technical_specificity": "medium",
            "iteration_support": "remix_and_parameter_based"
          },
          "example_composition": {
            "structure": "[Subject], [artistic style], [contextual elements], [technical parameters]",
            "sample": "Ethereal forest guardian, art nouveau style, vibrant colors, detailed textures --ar 2:3 --v 6 --stylize 600"
          }
        },
        "flux": {
          "archetype": "hybrid_technical_natural",
          "strategy_profile": {
            "approach": "extended_descriptive_with_technical_precision",
            "syntax_requirements": "moderate_technical_knowledge",
            "refinement_method": "detailed_narrative_modification",
            "consistency_mechanism": "long_prompt_coherence_and_text_integration"
          },
          "composition_structure": {
            "primary_descriptor": "detailed_narrative_description",
            "technical_integration": "embedded_without_weighting",
            "text_handling": "extended_text_elements_supported",
            "structural_emphasis": "isometric_and_schematic_friendly"
          },
          "optimal_patterns": {
            "length": "long_detailed_descriptions",
            "tone": "technical_narrative",
            "technical_specificity": "high",
            "iteration_support": "narrative_modification"
          },
          "example_composition": {
            "structure": "[Detailed scene with technical elements], [viewing perspective], [style specification]",
            "sample": "A detailed schematic of a futuristic drone assembly line, including annotated components and workflow arrows, in isometric view with clean lines"
          }
        },
        "runway_gen_3": {
          "archetype": "motion_oriented_temporal",
          "strategy_profile": {
            "approach": "dynamic_scene_description_with_temporal_elements",
            "syntax_requirements": "moderate_cinematic_knowledge",
            "refinement_method": "temporal_parameter_adjustment",
            "consistency_mechanism": "motion_aware_coherence_across_frames"
          },
          "composition_structure": {
            "primary_descriptor": "scene_with_motion_elements",
            "temporal_integration": "transition_and_action_descriptions",
            "parameter_format": "--duration --aspect --style",
            "cinematic_emphasis": "camera_movement_and_lighting_awareness"
          },
          "optimal_patterns": {
            "length": "medium_with_motion_descriptors",
            "tone": "cinematic_descriptive",
            "technical_specificity": "medium_to_high",
            "iteration_support": "temporal_parameter_based"
          },
          "example_composition": {
            "structure": "[Dynamic scene description], [temporal transitions], [cinematic style], [technical parameters]",
            "sample": "A serene mountain landscape transitioning from dawn to dusk, with flowing rivers and drifting clouds, in cinematic style --duration 5s --aspect 16:9"
          }
        }
      },
      "cross_model_optimization_guidelines": {
        "model_selection_criteria": {
          "conversational_simplicity": "gpt_4o",
          "technical_parameterization": "stable_diffusion",
          "artistic_control": "midjourney",
          "hybrid_text_structural": "flux",
          "motion_temporal_coherence": "runway_gen_3"
        },
        "universal_best_practices": {
          "specificity": "increase_detail_for_better_adherence",
          "iteration": "use_model_native_refinement_methods",
          "consistency": "leverage_model_specific_consistency_mechanisms",
          "workflow_alignment": "match_archetype_to_intended_output_type"
        }
      }
    }

<!-- ======================================================= -->
<!-- [2025.08.03 17:06] -->
<!-- gemini-pro -->

    [
      {
        "model_name": "GPT-4o",
        "prompt_archetype": "Natural Language",
        "strategy_summary": "Employs conversational, descriptive prompts that can be refined iteratively through follow-up instructions. Focuses on context retention and high adherence to natural language.",
        "key_components": [
          "Descriptive Scene",
          "Action/Subject",
          "Atmosphere/Mood",
          "Iterative Refinement Command"
        ],
        "example_prompt": "Generate a photorealistic image of a Victorian-era library with floating books and warm candlelight, then adjust to add a reading cat in the corner."
      },
      {
        "model_name": "Stable Diffusion",
        "prompt_archetype": "Technical",
        "strategy_summary": "Relies on parameterized prompts with weighted keywords, negative prompts, and explicit technical controls for reproducible, fine-grained outputs.",
        "key_components": [
          "Primary Subject",
          "Descriptive Qualifiers",
          "Weighted Keywords (e.g., (word:1.3))",
          "Negative Prompts (--no)",
          "Technical Parameters (--ar, --cfg)"
        ],
        "example_prompt": "Cyberpunk cityscape at night, neon lights, rainy streets, high detail, (photorealistic:1.3), --ar 16:9 --v 5 --q 2"
      },
      {
        "model_name": "Midjourney",
        "prompt_archetype": "Artistic",
        "strategy_summary": "Favors concise, ordered phrases that prioritize artistic style and composition. Uses specific parameters for aspect ratio, version, and stylization.",
        "key_components": [
          "Subject",
          "Style Descriptor",
          "Composition/Lighting",
          "Parameters (--ar, --v, --stylize)"
        ],
        "example_prompt": "Ethereal forest guardian, art nouveau style, vibrant colors, detailed textures --ar 2:3 --v 6 --stylize 600"
      },
      {
        "model_name": "Flux",
        "prompt_archetype": "Hybrid",
        "strategy_summary": "Blends descriptive natural language with the precision of technical prompts, excelling at integrating coherent text and structural elements without complex weighting.",
        "key_components": [
          "Core Concept",
          "Structural Details",
          "Integrated Text Elements",
          "Viewpoint/Perspective"
        ],
        "example_prompt": "A detailed schematic of a futuristic drone assembly line, including annotated components and workflow arrows, in isometric view with clean lines."
      },
      {
        "model_name": "Runway Gen-3",
        "prompt_archetype": "Motion-Oriented",
        "strategy_summary": "Focuses on prompts that describe temporal sequences, motion, and dynamic scene evolution for video generation, while supporting static image generation.",
        "key_components": [
          "Scene Description",
          "Action/Motion Verb",
          "Temporal Element (e.g., 'transitioning from dawn to dusk')",
          "Cinematic Style",
          "Parameters (--duration, --aspect)"
        ],
        "example_prompt": "A serene mountain landscape transitioning from dawn to dusk, with flowing rivers and drifting clouds, in cinematic style --duration 5s --aspect 16:9"
      }
    ]

<!-- ======================================================= -->
<!-- [2025.08.03 17:07] -->
<!-- groq4 -->

    {
      "prompting_strategies": {
        "Midjourney": {
          "archetype": "Artistic",
          "core_paradigm": "Concise, evocative phrases with ordered elements: subject → style → parameters; supports remix flags for variations and personalization codes for consistency.",
          "key_strategies": [
            "Structure prompts logically: start with main subject, add artistic style, then technical parameters like --ar, --v, --stylize.",
            "Use precise, evocative language to evoke artistic outputs; avoid verbosity.",
            "Leverage personalization (e.g., --p code) for user-aligned consistency.",
            "Incorporate remix flags (--remix) for iterative modifications without full rephrasing."
          ],
          "example": "Ethereal forest guardian, art nouveau style, vibrant colors, detailed textures --ar 2:3 --v 6 --stylize 600"
        },
        "Stable Diffusion": {
          "archetype": "Technical",
          "core_paradigm": "Parameterized, weighted descriptors with explicit controls like keyword weights, negative prompts, and sampling parameters for reproducible consistency.",
          "key_strategies": [
            "Apply weights to keywords (e.g., (keyword:1.2)) to emphasize elements.",
            "Include technical flags like --ar for aspect ratio, --v for version, --q for quality.",
            "Use negative prompts to exclude unwanted elements for output consistency.",
            "Fix seeds and CFG scales for deterministic, reproducible generations."
          ],
          "example": "Cyberpunk cityscape at night, neon lights, rainy streets, high detail, (photorealistic:1.3), --ar 16:9 --v 5 --q 2"
        },
        "Runway Gen-3": {
          "archetype": "Motion-oriented",
          "core_paradigm": "Descriptive prompts incorporating temporal elements and action sequences, with parameters for style consistency across dynamic generations.",
          "key_strategies": [
            "Integrate motion descriptors (e.g., transitions, durations) for temporal coherence.",
            "Specify style and aspect ratios (e.g., --duration, --aspect) to maintain consistency in sequences.",
            "Focus on action-oriented language to reduce artifacts in dynamic outputs.",
            "Use iterative prompts for frame-by-frame refinements in video-to-image workflows."
          ],
          "example": "A serene mountain landscape transitioning from dawn to dusk, with flowing rivers and drifting clouds, in cinematic style --duration 5s --aspect 16:9"
        },
        "GPT-4o": {
          "archetype": "Natural language",
          "core_paradigm": "Descriptive, conversational inputs with chain-of-thought refinement for iterative, context-aware consistency.",
          "key_strategies": [
            "Employ natural, detailed descriptions in conversational style.",
            "Use follow-up prompts for refinements (e.g., 'then adjust to add...').",
            "Leverage context retention for minimal deviation in sequential modifications.",
            "Incorporate chain-of-thought for complex compositions to enhance adherence."
          ],
          "example": "Generate a photorealistic image of a Victorian-era library with floating books and warm candlelight, then adjust to add a reading cat in the corner"
        },
        "Flux": {
          "archetype": "Hybrid",
          "core_paradigm": "Blends technical precision with natural descriptions, excelling in extended text integration and long-prompt handling without explicit weighting.",
          "key_strategies": [
            "Combine descriptive narratives with precise structural details.",
            "Handle lengthy prompts for text coherence and reduced hallucinations.",
            "Avoid weights; rely on natural phrasing for integrated text-image elements.",
            "Use isometric or schematic views for operational consistency in designs."
          ],
          "example": "A detailed schematic of a futuristic drone assembly line, including annotated components and workflow arrows, in isometric view with clean lines"
        }
      }
    }

