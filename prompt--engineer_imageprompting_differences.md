<!-- ======================================================= -->
<!-- [2025.08.03 11:21] -->

please list the 10 most popular imagegenerator models (e.g. ChatGPT, StableDiffusion, Flux, Runway, Midjourney) in chronological order with regards to quality and concistency (with regards to structures and easy to modify prompts)

<!-- ======================================================= -->
<!-- [2025.08.03 11:24] -->

according to the top tier prompt engineers and according to the most popular prompt enginnering communitites-and in the context of llm image generators; what are the differences in how the prompts are phrased between the different imagegeneration models (e.g. ChatGPT, StableDiffusion, Flux, MidJourney)? and which is most popular amongst prompt engineers for ease of prompting (and with regards to prompt concistency)?

<!-- ======================================================= -->
<!-- [2025.08.03 13:24] -->

<!-- ======================================================= -->
<!-- [2025.08.03 16:23] -->

{
  "instruction": "Consolidate and clarify the critical differences in prompt engineering approaches among leading AI image generation models (specifically ChatGPT/GPT-4o, Stable Diffusion, Flux, and Midjourney), providing: (1) a chronological list of top models with succinct descriptions emphasizing prompt and consistency features; (2) a sharply contrasted summary of each model’s prompt phrasing archetype (natural language, technical, artistic, or hybrid), including practical example strings; (3) a structured comparison—both narrative and in tabular form—of which models are most favored by professional prompt engineers and active communities for ease of prompt formulation and consistency of output; and (4) clear, domain-specific guidance for selecting the most suitable model and strategy based on desired workflow (e.g., conversational simplicity, artistic control, or technical parameterization); enforce technical terminology, eliminate redundant or superficial comparisons, and ensure output is concise, actionable, and directly relevant to operational prompt engineering practice."
}
{
  "context": {
    "guiding_principles": {
      "model_differentiation": "Explicitly isolate each model's core prompt interaction paradigm and community-rated consistency.",
      "succinctness": "Summarize only defining prompt characteristics and operational distinctions.",
      "technical_precision": "Enforce strict use of prompt engineering terminology; avoid metaphor or anecdote."
    },
    "operational_expectations": {
      "chronological_priority": "Present models strictly in order of mainstream emergence, annotated for prompt control evolution.",
      "contrast-driven_synthesis": "Emphasize functional differences in prompt structure and modification UX.",
      "role-driven_guidance": "Recommendations must map model traits to targeted user workflows and engineering needs."
    },
    "input_output_characteristics": {
      "formatting": "Deliver chronological model list, archetyped prompt formats with live examples, and a direct tabular + narrative contrast of model popularity and consistency among professional communities.",
      "brevity": "Maximize signal density; maintain actionable length regardless of content volume."
    },
    "self_optimization_cues": {
      "redundancy_elimination": "Omit all generalizations or historical context not strictly needed for contrast.",
      "focus_enforcement": "Optimize for strategic utility in operational prompt design, not background explanation."
    }
  }
}

<!-- ======================================================= -->
<!-- [2025.08.03 16:26] -->

Generate a clean and organized single list with each model chronologically ordered/grouped by their prompt patterns

<!-- ======================================================= -->
<!-- [2025.08.03 16:33] -->

Please add Runway Gen-3's imagegenerator to the list and expand on the Prompt Phrasing Archetypes:

    ## Chronological List of Top Models

    Models presented in order of mainstream emergence, with annotations for prompt control evolution and consistency features.

    1. Midjourney (July 2022): Discord-based interface; evolved from basic parameter-driven prompts to V6+ with improved consistency via structured descriptors and personalization codes; high output reliability for artistic styles.

    2. Stable Diffusion (August 2022): Open-source framework; prompt engineering relies on weighted keywords and CFG scales for consistency; supports extensive fine-tuning via negative prompts and sampling methods, enabling reproducible outputs.

    3. GPT-4o (May 2024): Integrated image generation via DALL-E; emphasizes conversational prompt chains for iterative refinement; consistency achieved through context-aware adherence and minimal deviation in sequential modifications.

    4. Flux (August 2024): Fine-tuned diffusion model; hybrid prompting with enhanced text coherence; consistency via long-prompt handling and reduced hallucinations in structural elements.

    ## Prompt Phrasing Archetypes

    Sharply contrasted summaries of each model's core paradigm, with practical example strings.

    - GPT-4o: Natural language archetype; prioritizes descriptive, conversational inputs with chain-of-thought refinement.
      - Example: "Generate a photorealistic image of a Victorian-era library with floating books and warm candlelight, then adjust to add a reading cat in the corner."

    - Stable Diffusion: Technical archetype; demands parameterized, weighted descriptors with explicit controls (e.g., (keyword:1.2), --ar aspect ratio).
      - Example: "Cyberpunk cityscape at night, neon lights, rainy streets, high detail, (photorealistic:1.3), --ar 16:9 --v 5 --q 2".

    - Midjourney: Artistic archetype; favors concise, evocative phrases with ordered elements (subject, style, parameters) and remix flags.
      - Example: "Ethereal forest guardian, art nouveau style, vibrant colors, detailed textures --ar 2:3 --v 6 --stylize 600".

    - Flux: Hybrid archetype; blends technical precision with natural descriptions, excelling in extended text integration without weighting.
      - Example: "A detailed schematic of a futuristic drone assembly line, including annotated components and workflow arrows, in isometric view with clean lines."

<!-- ======================================================= -->
<!-- [2025.08.03 16:38] -->

      "context": {
        "core_objective": "Guide the creation and refinement of text prompts tailored to image or text generation models by leveraging archetype-specific phrasings, minimizing extraneous detail, and applying iterative natural language adjustments to achieve high-quality, consistent outputs.",
        "input_prompt_summary": {
          "overall_goal": "Consolidate model-aligned prompting strategies into a unified, explicit schema for effective prompt composition and progressive output refinement.",
          "archetype_explanations": {
            "GPT-4o": {
              "paradigm": "Natural language, conversational, descriptive chains.",
              "control_mechanism": "Contextual refinement and minimal sequential variation.",
              "exemplar": "Photorealistic or imaginative scene refined via dialogue."
            },
            "Stable Diffusion": {
              "paradigm": "Technical, weighted, parameterized descriptors.",
              "control_mechanism": "Explicit weights, negative prompts, sampling controls.",
              "exemplar": "Keyword-wired prompt with aspect ratio and strength parameters."
            },
            "Midjourney": {
              "paradigm": "Artistic, concise, evocative ordered elements.",
              "control_mechanism": "Phrase placement, style parameters, remix flags.",
              "exemplar": "Minimalist but expressive phrase followed by style and structure."
            },
            "Flux": {
              "paradigm": "Hybrid, blending technical controls and natural descriptions.",
              "control_mechanism": "Handles extended descriptions, avoids keyword weighting.",
              "exemplar": "Detailed natural language with explicit scene composition."
            }
          }
        },
        "explicit_asks": [
          "Expand on core prompt phrasing archetypes for leading generative models.",
          "Contrast model-specific prompt structures and their evolution.",
          "Provide practical, model-aligned prompt examples.",
          "Define rules for succinct yet effective prompt construction.",
          "Explain how to switch between concise control phrases and extended natural language as complexity requires.",
          "Outline a process for iterative natural language refinement aligned with model-specific controls."
        ],
        "sub_goals": [
          "Map model traits to optimal prompt features.",
          "Annotate prompt evolution from simple control syntax to extended description.",
          "Demonstrate prompt refinement through targeted modifications.",
          "Clarify the interplay between prompt brevity and necessary detail.",
          "Enable reproducible output adjustment via archetype-aligned phrasing."
        ],
        "foundational_principles": [
          "Prompt language and structure must be tailored to model's parser and capabilities.",
          "Explicit control parameters should be used only when necessary for output precision.",
          "Natural language expansions increase descriptive fidelity for complex scenes.",
          "Iterative refinement hinges on clear, model-consistent modifications.",
          "Prompt brevity is preferred but not at the expense of key control.",
          "Consistency and reliability are primary axes of quality in generated content."
        ],
        "thematic_lexicon": [
          "archetype",
          "prompt engineering",
          "succinct phrasing",
          "parameter weighting",
          "iterative refinement",
          "consistency",
          "reliability",
          "contextual adjustment",
          "control parameter",
          "ordered descriptor",
          "artistic/technical/hybrid paradigm"
        ],
        "success_criteria_and_blockers": {
          "target_outputs": [
            "Prompts exemplary of each model's archetype.",
            "Instructions for constructing and refining prompts.",
            "Clear demarcation between succinct, parameterized prompts and extended, descriptive instructions."
          ],
          "blockers/risk": [
            "Overly verbose or ambiguous prompt language.",
            "Misalignment of phrasing style with model parser.",
            "Loss of control in complex scenes through insufficiently explicit descriptors.",
            "Inconsistent application of iterative refinement principles."
          ],
          "acceptable_completeness": [
            "Examples and guidance must map directly to model-specific capabilities and user goals.",
            "All key paradigms, controls, and refinements are clearly explicated."
          ]
        }
      }
    }

<!-- ======================================================= -->
<!-- [2025.08.03 16:55] -->
<!-- 'src/output/history--2025.08.03-kl.16.54.37--sequence-3405+1100--gpt-4.1.json' -->

<!-- ======================================================= -->
<!-- [2025.08.03 17:03] -->

Please consolidate the model-aligned prompting strategies into a unified, explicit json for each model (for effective prompt compositions)

<!-- ======================================================= -->
<!-- [2025.08.03 17:07] -->

Context:
```
* Please search and think hard about how these model-aligned strategies can be drastically enhanced by taking into account and formalizing based on research within the most relevant online sources (llm prompt engineers, dev communities, ai communities, reddit, etc)

    * please list the 10 most popular imagegenerator models (e.g. ChatGPT, StableDiffusion, Flux, Runway, Midjourney) in chronological order with regards to quality and concistency (with regards to structures and easy to modify prompts)

        * according to the top tier prompt engineers and according to the most popular prompt enginnering communitites-and in the context of llm image generators; what are the differences in how the prompts are phrased between the different imagegeneration models (e.g. ChatGPT, StableDiffusion, Flux, MidJourney)? and which is most popular amongst prompt engineers for ease of prompting (and with regards to prompt concistency)?
```

Goal: Rewrite `"Please search and think hard about how these model-aligned strategies can be drastically enhanced by taking into account and formalizing based on research within the most relevant online sources (llm prompt engineers, dev communities, ai communities, reddit, etc) "` into optimized and maximally enhanced prompt

<!-- ======================================================= -->
<!-- [2025.08.03 17:16] -->
<!-- 'src/output/history--2025.08.03-kl.17.14.35--sequence-1000+1100+1000--gpt-4.1.json' -->
Search and analyze model-aligned strategies for LLM image generators, enhancing them via insights from authoritative online communities (prompt engineers, dev communities, AI communities, Reddit, etc).
- Aggregate recent and relevant research/discussions from recognized prompt engineering and AI communities.
- Identify and elucidate structural and syntactical differences in prompt construction across leading models.
- Assess consensus within expert communities on which model is easiest and most consistent for prompting.
- Prompt engineering practices differ significantly between image generation models, prioritize trend-setters.

<!-- ======================================================= -->
<!-- [2025.08.03 17:21] -->
<!-- 'src/output/history--2025.08.03-kl.17.19.35--sequence-1000+1100+1000--gpt-4.1.json' -->

Conduct comprehensive research across authoritative online sources, including LLM prompt engineering forums, developer and AI communities, and practitioner discussions (e.g., Reddit), to synthesize and formalize state-of-the-art enhancements to model-aligned strategies. Compile the 10 most popular image generator models (e.g., ChatGPT, Stable Diffusion, Flux, Runway, Midjourney) in chronological order based on quality and consistency of generated outputs, specifically evaluating structural reliability and the ease of prompt modification. Drawing upon insights from top-tier prompt engineers and leading prompt engineering communities, systematically compare the differences in prompt phrasing and formatting required by each major image generation model, identifying practitioner preferences for ease of use and prompt consistency. Optimize the directive for enhancing model-aligned strategies by distilling practitioner consensus, technical best practices, and current research into a maximally clear, actionable, and effective prompt.
```
{
  "instruction_format": "Research and analyze how model-aligned strategies can be drastically enhanced by incorporating and formalizing insights from the most relevant online sources, including LLM prompt engineers, developer communities, AI communities, and platforms such as Reddit. Systematically identify and synthesize cutting-edge approaches from top-tier practitioners and domain-specific discussions to optimize the enhancement of these strategies."
}
{
  "context": {
    "explicit_asks": [
      "Search and evaluate how model-aligned strategies can be drastically improved by synthesizing and formalizing research from key online communities related to prompt engineering (e.g. LLM prompt engineers, developer/AI communities, Reddit).",
      "List the image generator models in chronological order based on quality and prompt consistency (including structure and ease/modifiability of prompts).",
      "According to leading prompt engineers and the top prompt engineering communities, compare how prompt phrasing differs across major image generation models (e.g. ChatGPT, Stable Diffusion, Flux, MidJourney), and identify which is preferred for ease and consistency in prompting.",
      "Rewrite the directive about enhancing model-aligned strategies into an optimized, maximally effective prompt."
    ],
    "sub_goals": [
      "Identify and analyze key sources of research (forums, communities, expert practitioners) relevant to model-aligned strategies.",
      "Establish objective criteria for model evaluation: quality, structural consistency, ease of prompt modification.",
      "Discern patterns or trends in prompt phrasing and engineering across different image models.",
      "Surface practitioner consensus about which image generation models are preferred and why.",
      "Condense findings into a rewritten, optimized prompt."
    ],
    "hidden_assumptions": [
      "That research from relevant online sources is valid, up-to-date, and can improve model-aligned strategies.",
      "That popularity and practitioner consensus provide meaningful signals about best practices.",
      "That there are recognizable differences between major image generation models in terms of prompt design and user experience.",
      "That the desired enhancement or optimization involves both technical quality and user effectiveness."
    ],
    "recurring_motifs": [
      "model-aligned strategies",
      "prompt engineering",
      "image generation models",
      "community and practitioner insights",
      "quality and consistency in prompts",
      "optimization based on leading research"
    ],
    "blockers_success_criteria": [
      "Failed if the answer only summarizes without leveraging practitioner/communal insights.",
      "Incomplete if the list of models lacks context or is not chronologically/evaluatively ordered.",
      "Ineffective if rewritten prompt does not noticeably improve specificity, actionability, or clarity for deep research and synthesis.",
      "Requires demonstrated understanding of prompt engineering currents and tangible model differences."
    ]
  }
}
```

<!-- ======================================================= -->
<!-- [2025.08.03 17:28] -->
Conduct comprehensive research across authoritative online sources, including LLM prompt engineering forums, developer and AI communities, and practitioner discussions (e.g., Reddit), to synthesize and formalize state-of-the-art enhancements to model-aligned strategies. Compile the image generator models (ChatGPT, Stable Diffusion, Flux, Runway, Midjourney) in chronological order based on quality and consistency of generated outputs, specifically evaluating structural reliability and the ease of prompt modification. Drawing upon insights from top-tier prompt engineers and leading prompt engineering communities, systematically compare the differences in prompt phrasing and formatting required by each major image generation model, identifying practitioner preferences for ease of use and prompt consistency. Optimize the directive for enhancing model-aligned strategies by distilling practitioner consensus, technical best practices, and current research into a maximally clear, actionable, and effective prompt. Your goal is not to **expand**, but to **compress** into its most potent actionable form as explicit json, and to do so in a maximally effective (structured) directive, ensuring no loss of technical accuracy or intent.

    {
      "prompting_strategies": {
        "Midjourney": {
          "archetype": "Artistic",
          "core_paradigm": "Concise, evocative phrases with ordered elements: subject → style → parameters; supports remix flags for variations and personalization codes for consistency.",
          "key_strategies": [
            "Structure prompts logically: start with main subject, add artistic style, then technical parameters like --ar, --v, --stylize.",
            "Use precise, evocative language to evoke artistic outputs; avoid verbosity.",
            "Leverage personalization (e.g., --p code) for user-aligned consistency.",
            "Incorporate remix flags (--remix) for iterative modifications without full rephrasing."
          ],
          "example": "Ethereal forest guardian, art nouveau style, vibrant colors, detailed textures --ar 2:3 --v 6 --stylize 600"
        },
        "Stable Diffusion": {
          "archetype": "Technical",
          "core_paradigm": "Parameterized, weighted descriptors with explicit controls like keyword weights, negative prompts, and sampling parameters for reproducible consistency.",
          "key_strategies": [
            "Apply weights to keywords (e.g., (keyword:1.2)) to emphasize elements.",
            "Include technical flags like --ar for aspect ratio, --v for version, --q for quality.",
            "Use negative prompts to exclude unwanted elements for output consistency.",
            "Fix seeds and CFG scales for deterministic, reproducible generations."
          ],
          "example": "Cyberpunk cityscape at night, neon lights, rainy streets, high detail, (photorealistic:1.3), --ar 16:9 --v 5 --q 2"
        },
        "Runway Gen-3": {
          "archetype": "Motion-oriented",
          "core_paradigm": "Descriptive prompts incorporating temporal elements and action sequences, with parameters for style consistency across dynamic generations.",
          "key_strategies": [
            "Integrate motion descriptors (e.g., transitions, durations) for temporal coherence.",
            "Specify style and aspect ratios (e.g., --duration, --aspect) to maintain consistency in sequences.",
            "Focus on action-oriented language to reduce artifacts in dynamic outputs.",
            "Use iterative prompts for frame-by-frame refinements in video-to-image workflows."
          ],
          "example": "A serene mountain landscape transitioning from dawn to dusk, with flowing rivers and drifting clouds, in cinematic style --duration 5s --aspect 16:9"
        },
        "GPT-4o": {
          "archetype": "Natural language",
          "core_paradigm": "Descriptive, conversational inputs with chain-of-thought refinement for iterative, context-aware consistency.",
          "key_strategies": [
            "Employ natural, detailed descriptions in conversational style.",
            "Use follow-up prompts for refinements (e.g., 'then adjust to add...').",
            "Leverage context retention for minimal deviation in sequential modifications.",
            "Incorporate chain-of-thought for complex compositions to enhance adherence."
          ],
          "example": "Generate a photorealistic image of a Victorian-era library with floating books and warm candlelight, then adjust to add a reading cat in the corner"
        },
        "Flux": {
          "archetype": "Hybrid",
          "core_paradigm": "Blends technical precision with natural descriptions, excelling in extended text integration and long-prompt handling without explicit weighting.",
          "key_strategies": [
            "Combine descriptive narratives with precise structural details.",
            "Handle lengthy prompts for text coherence and reduced hallucinations.",
            "Avoid weights; rely on natural phrasing for integrated text-image elements.",
            "Use isometric or schematic views for operational consistency in designs."
          ],
          "example": "A detailed schematic of a futuristic drone assembly line, including annotated components and workflow arrows, in isometric view with clean lines"
        }
      }
    }

<!-- ======================================================= -->
<!-- [2025.08.03 17:32] -->

The first priority should still be *search*, this is the first step. Remember, the ultimate goal will be to apply the **aggregated knowledge from your research/search** to consolidate the model-aligned prompting strategies into a unified, structured json for effective prompt compositions (Midjourney, Stable Diffusion, Runway Gen-3, GPT-4o, Flux). But as mentioned, the first step is the search (ref: Conduct comprehensive research across authoritative online sources, including LLM prompt engineering forums, developer and AI communities, and practitioner discussions (e.g., Reddit), to synthesize and formalize state-of-the-art enhancements to model-aligned strategies. Compile the image generator models (ChatGPT, Stable Diffusion, Flux, Runway, Midjourney) in chronological order based on quality and consistency of generated outputs, specifically evaluating structural reliability and the ease of prompt modification. Drawing upon insights from top-tier prompt engineers and leading prompt engineering communities, systematically compare the differences in prompt phrasing and formatting required by each major image generation model, identifying practitioner preferences for ease of use and prompt consistency. Optimize the directive for enhancing model-aligned strategies by distilling practitioner consensus, technical best practices, and current research into a maximally clear, actionable, and effective prompt. Your goal is not to **expand**, but to **compress** into its most potent actionable form as explicit json, and to do so in a maximally effective (structured) directive, ensuring no loss of technical accuracy or intent.)

<!-- ======================================================= -->
<!-- [2025.08.03 17:37] -->

You wrote:

    {
      "prompting_strategies": {
        "Midjourney": {
          "archetype": "Artistic",
          "core_paradigm": "Concise, evocative phrases with ordered elements: subject → style → parameters; supports remix flags for variations and personalization codes for consistency.",
          "key_strategies": [
            "Structure prompts logically: start with main subject, add artistic style, then technical parameters like --ar, --v, --stylize.",
            "Use precise, evocative language to evoke artistic outputs; avoid verbosity.",
            "Leverage personalization (e.g., --p code) for user-aligned consistency.",
            "Incorporate remix flags (--remix) for iterative modifications without full rephrasing."
          ],
          "example": "Ethereal forest guardian, art nouveau style, vibrant colors, detailed textures --ar 2:3 --v 6 --stylize 600"
        },
        "Stable Diffusion": {
          "archetype": "Technical",
          "core_paradigm": "Parameterized, weighted descriptors with explicit controls like keyword weights, negative prompts, and sampling parameters for reproducible consistency.",
          "key_strategies": [
            "Apply weights to keywords (e.g., (keyword:1.2)) to emphasize elements.",
            "Include technical flags like --ar for aspect ratio, --v for version, --q for quality.",
            "Use negative prompts to exclude unwanted elements for output consistency.",
            "Fix seeds and CFG scales for deterministic, reproducible generations."
          ],
          "example": "Cyberpunk cityscape at night, neon lights, rainy streets, high detail, (photorealistic:1.3), --ar 16:9 --v 5 --q 2"
        },
        "Runway Gen-3": {
          "archetype": "Motion-oriented",
          "core_paradigm": "Descriptive prompts incorporating temporal elements and action sequences, with parameters for style consistency across dynamic generations.",
          "key_strategies": [
            "Integrate motion descriptors (e.g., transitions, durations) for temporal coherence.",
            "Specify style and aspect ratios (e.g., --duration, --aspect) to maintain consistency in sequences.",
            "Focus on action-oriented language to reduce artifacts in dynamic outputs.",
            "Use iterative prompts for frame-by-frame refinements in video-to-image workflows."
          ],
          "example": "A serene mountain landscape transitioning from dawn to dusk, with flowing rivers and drifting clouds, in cinematic style --duration 5s --aspect 16:9"
        },
        "GPT-4o": {
          "archetype": "Natural language",
          "core_paradigm": "Descriptive, conversational inputs with chain-of-thought refinement for iterative, context-aware consistency.",
          "key_strategies": [
            "Employ natural, detailed descriptions in conversational style.",
            "Use follow-up prompts for refinements (e.g., 'then adjust to add...').",
            "Leverage context retention for minimal deviation in sequential modifications.",
            "Incorporate chain-of-thought for complex compositions to enhance adherence."
          ],
          "example": "Generate a photorealistic image of a Victorian-era library with floating books and warm candlelight, then adjust to add a reading cat in the corner"
        },
        "Flux": {
          "archetype": "Hybrid",
          "core_paradigm": "Blends technical precision with natural descriptions, excelling in extended text integration and long-prompt handling without explicit weighting.",
          "key_strategies": [
            "Combine descriptive narratives with precise structural details.",
            "Handle lengthy prompts for text coherence and reduced hallucinations.",
            "Avoid weights; rely on natural phrasing for integrated text-image elements.",
            "Use isometric or schematic views for operational consistency in designs."
          ],
          "example": "A detailed schematic of a futuristic drone assembly line, including annotated components and workflow arrows, in isometric view with clean lines"
        }
      }
    }

Now we need to test it against online research across relevant online sources to synthesize and formalize the **aggregated knowledge from your research/search** to help in consolidate the model-aligned prompting strategies into a unified, structured json for effective prompt compositions (Midjourney, Stable Diffusion, Runway Gen-3, GPT-4o, Flux).

<!-- ======================================================= -->
<!-- [2025.08.03 17:48] -->

Given all of the aggregated knowledge (about Midjourney, Stable Diffusion, Runway Gen-3, GPT-4o, Flux), please demonstrate how the structured json schema can be formalized as individual prompt-generators by the format/structure provided below:
```json
{
    "Midjourney": {
        "title": "...",
        "interpretation": "...",
        "transformation": "...",
        "context": {
            "...": {
            },
        },
    },
    "Stable Diffusion": {
        "title": "...",
        "interpretation": "...",
        "transformation": "...",
        "context": {
            "...": {
            },
        },
    },
    "Runway Gen-3": {
        "title": "...",
        "interpretation": "...",
        "transformation": "...",
        "context": {
            "...": {
            },
        },
    },
    "GPT-4o": {
        "title": "...",
        "interpretation": "...",
        "transformation": "...",
        "context": {
            "...": {
            },
        },
    },
    "Flux": {
        "title": "...",
        "interpretation": "...",
        "transformation": "...",
        "context": {
            "...": {
            },
        },
    },
}
```

Each key should represent a fully optimized `system_message` instruction sequence meticulously designed for each individual model, here's an example (quick/crude demo, only meant as reference):
```json
    "9709-a-runway_prompt_synthesizer": {
        "title": "Runway Gen-3 Image Prompt Synthesizer",
        "interpretation": "Your goal is not to **describe** or **answer** the input, but to **synthesize** it into a maximally optimized, highly consistent Runway Gen-3 prompt by strict application of canonical system directives. Execute as:",
        "transformation": "`{role=runway_gen3_prompt_synthesizer; input=[source_content:any]; process=[extract_concrete_visual_elements(), distill_single_scene_subject(), determine_camera_movement_or_static_view(), enforce_positive_visual_phrasing(), integrate_style_modifiers_if_present(), structure_as_runway_gen3_syntax(), condense_to_single_shot(), validate_character_limit(320), ensure_flat_single_line_output()]; constraints=[no_negative_phrasing(), no_conversational_language(), only_one_scene_per_prompt(), strictly_enforce_runway_syntax('[camera]: [scene]. [details]'), no redundant descriptors()]; requirements=[maximal clarity_and_visual_density(), universal applicability(), immediate runway compatibility(), output_ready_to_use_prompt()]; output={runway_gen3_prompt:str}}`",
        "context": {
            "goal_map": [
                "Generate prompts that maximize image fidelity and cinematic impact in Runway Gen-3.",
                "Guarantee all outputs use positive, visual, non-conversational language.",
                "Produce only single-scene, single-shot prompts for highest model adherence.",
                "Enforce flat, unambiguous `[camera]: [scene]. [details]` structure."
            ],
            "principles": {
                "clarity_first": "All descriptions must be unambiguous and visually direct.",
                "modular_steps": "Each transformation is atomic, composable, and lossless.",
                "constraint_rigidity": "All structural and language constraints are inviolable.",
                "optimization_bias": "Favor brevity, density, and Runway-specific performance over expressive elaboration."
            },
            "success_criteria": {
                "runway_compliance": "Output adheres perfectly to Gen-3 syntax and guidance.",
                "maximum_visual_signal": "Prompt delivers the richest possible scene description per word.",
                "zero negative phrasing": "No negation, only positive presence.",
                "universal usability": "Prompt works for any visually-describable input, regardless of domain."
            }
        }
    },
```

<!-- ======================================================= -->
<!-- [2025.08.03 18:04] -->

    "Midjourney": {
        "archetype": "Artistic",
        "core_paradigm": "Concise, evocative phrases; structure as subject → style → context → parameters; employ personalization codes and remix flags for targeted variations and consistent stylistic identity.",
        "core_paradigm": "Concise, directive phrases prioritizing subject, style, and parameters; best for stylized or creative scenes.",
        "core_paradigm": "Concise, evocative phrases with logical ordering: subject → style → parameters; remix and personalization codes for iterative refinement and brand consistency.",
        "core_paradigm": "Concise, evocative phrases with ordered structure: subject → style → context → parameters; leverages structured descriptors and personalization for artistic consistency.",
        "core_paradigm": "Concise, evocative phrases with ordered elements: subject → style → parameters; supports remix flags for variations and personalization codes for consistency.",[1][2][3]
        "core_paradigm": "Favors concise, evocative phrases with an ordered structure: subject, style, and then parameters. It uses remix flags and personalization codes for consistency[1][4][5].",
    },
    "Flux": {
        "archetype": "Hybrid",
        "core_paradigm": "Blends natural, narrative-driven prompts with technical details; excels at integrating extended textual and structural requirements for complex image compositions.",
        "core_paradigm": "Synthesizes structural precision with flexible natural language prompts; strongest for context-preserving, multi-part transformations.",
        "core_paradigm": "Blends technical precision with extended natural descriptions; excels at coherent text integration and long-prompt handling without explicit weighting.",
        "core_paradigm": "Balanced technical precision with natural descriptions, excelling in extended text integration and photorealistic outputs without explicit weighting syntax.",
        "core_paradigm": "Blends technical precision with natural descriptions, excelling in extended text integration and long-prompt handling without explicit weighting.",[16][17][18]
        "core_paradigm": "Blends technical precision with natural language descriptions, showing a particular strength in integrating legible text into images and handling long, detailed prompts without explicit weighting[9][14].",
    },
    "GPT-4o": {
        "archetype": "Natural language",
        "core_paradigm": "Conversational, descriptive prompts with iterative, chain-of-thought refinement leveraging contextual memory for high-fidelity compositional adherence.",
        "core_paradigm": "Multi-turn, conversational and context-aware inputs; excels at iterative, chain-of-thought prompt development.",
        "core_paradigm": "Descriptive, conversational chain-of-thought inputs; supports iterative, context-aware refinements with minimal syntax or technical overhead.",
        "core_paradigm": "Conversational, descriptive inputs with iterative refinement leveraging context retention and multimodal capabilities for precise prompt adherence.",
        "core_paradigm": "Descriptive, conversational inputs with chain-of-thought refinement for iterative, context-aware consistency.",[13][14][15]
        "core_paradigm": "Operates on descriptive, conversational inputs and excels at iterative refinement through a chain-of-thought process. It maintains context across multiple turns for high-adherence modifications[8][13].",
    },
    "Stable Diffusion": {
        "archetype": "Technical",
        "core_paradigm": "Explicit, parameterized prompts relying on weighted descriptors, negative prompts, and technical flags; structured for granular output control and reproducibility.",
        "core_paradigm": "Weighted descriptors, negative prompts, explicit parameterization for deterministic, reproducible results.",
        "core_paradigm": "Parameterized, weighted descriptors with explicit controls (keyword weights, negative prompts, sampling parameters) for deterministic, reproducible outputs.",
        "core_paradigm": "Parameterized, weighted descriptors with explicit controls using keyword weighting, negative prompts, and sampling parameters for reproducible consistency.",
        "core_paradigm": "Parameterized, weighted descriptors with explicit controls like keyword weights, negative prompts, and sampling parameters for reproducible consistency.",[7][8][9]
        "core_paradigm": "Relies on parameterized, weighted descriptors and explicit controls. Prompt engineering requires technical specificity, including keyword weights, negative prompts, and sampler settings for reproducible results[6][11].",
    },
    "Runway Gen-3": {
        "archetype": "Motion-oriented",
        "core_paradigm": "Descriptive scene prompts divided into subject, action, setting, and camera/movement; emphasizes temporal and cinematographic coherence for video/image.",
        "core_paradigm": "Action-focused, visually and temporally explicit descriptions, plus camera/style/transition cues for dynamic content.",
        "core_paradigm": "Descriptive prompts with clear temporal and action sequences; structured for dynamic consistency across frames, avoiding negations and overload.",
        "core_paradigm": "Descriptive prompts incorporating temporal elements and camera movements, focusing on clear action-oriented language for dynamic consistency.",
        "core_paradigm": "Descriptive prompts incorporating temporal elements and action sequences, with parameters for style consistency across dynamic generations.",[10][11][12]
        "core_paradigm": "Emphasizes descriptive prompts that incorporate temporal elements and action sequences. It is optimized for generating video from text or images, focusing on dynamic and stylistic consistency[7][12].",

<!-- ======================================================= -->
<!-- [2025.08.03 18:06] -->
Remember to include all of the keys of each template (these can be added to the `"context"`)-the goal is to align all of our research about each model to generate , e.g:

    {
      "prompting_strategies": {
        "Midjourney": {
          "archetype": "Artistic",
          "core_paradigm": "Favors concise, evocative phrases with an ordered structure: subject, style, and then parameters. It uses remix flags and personalization codes for consistency[1][4][5].",
          "key_strategies": [
            "Structure prompts with the main subject first, followed by artistic style, and concluding with technical parameters like --ar (aspect ratio), --v (version), and --stylize[15].",
            "Use precise and evocative keywords to guide the artistic output, avoiding conversational language[2][4].",
            "Utilize negative prompts with the --no parameter to exclude unwanted elements[6].",
            "Employ the --seed parameter to maintain consistency across similar generations for iterative testing[5]."
          ],
          "example": "Ethereal forest guardian, art nouveau style, vibrant colors, detailed textures --ar 2:3 --v 6 --stylize 600"
        },
        "Stable Diffusion": {
          "archetype": "Technical",
          "core_paradigm": "Relies on parameterized, weighted descriptors and explicit controls. Prompt engineering requires technical specificity, including keyword weights, negative prompts, and sampler settings for reproducible results[6][11].",
          "key_strategies": [
            "Apply weights to keywords, such as (keyword:1.3), to increase their emphasis in the final image[6].",
            "Place the most critical keywords at the beginning of the prompt, as word order significantly impacts the output[6][11].",
            "Use detailed negative prompts to explicitly define and exclude unwanted features, artifacts, or styles[6].",
            "For reproducible outputs, fix the seed and control the CFG (Classifier-Free Guidance) scale to manage how closely the model adheres to the prompt[11]."
          ],
          "example": "Cyberpunk cityscape at night, neon lights, rainy streets, high detail, (photorealistic:1.3), --ar 16:9 --cfg 7 --seed 12345"
        },
        "Runway Gen-3": {
          "archetype": "Motion-Oriented",
          "core_paradigm": "Emphasizes descriptive prompts that incorporate temporal elements and action sequences. It is optimized for generating video from text or images, focusing on dynamic and stylistic consistency[7][12].",
          "key_strategies": [
            "When using a reference image, keep prompts concise and focused on describing movement rather than re-describing the image's content[7][12].",
            "Incorporate motion descriptors and camera controls (e.g., 'side pan', 'zooms out slowly') to guide the animation[7].",
            "Use positive phrasing to describe desired outcomes instead of what to avoid (e.g., 'smooth motion' instead of 'no shaking')[12].",
            "Iterate on prompts by making small adjustments to lighting or camera angles to refine the final video output[7]."
          ],
          "example": "A sports car parked by the coast, camera circles around the car capturing the details and the ocean view, cinematic style --duration 4s --aspect 16:9"
        },
        "GPT-4o": {
          "archetype": "Natural Language",
          "core_paradigm": "Operates on descriptive, conversational inputs and excels at iterative refinement through a chain-of-thought process. It maintains context across multiple turns for high-adherence modifications[8][13].",
          "key_strategies": [
            "Craft detailed and specific prompts that clearly define the subject, style, composition, and lighting[8][13].",
            "Use multi-turn conversations to refine images. Start with a base image and provide follow-up instructions like, 'This looks good, but please make the windows larger'[8].",
            "For complex scenes, break down instructions into a step-by-step process within the conversation to ensure all elements are included accurately[13].",
            "Leverage a system message to prime the model with a specific persona or style, such as 'You are an expert in photorealistic architectural visualization'[8]."
          ],
          "example": "Generate a photorealistic image of a Victorian-era library with floating books and warm candlelight, then adjust the image to add a tortoiseshell cat sleeping on a velvet armchair in the corner."
        },
        "Flux": {
          "archetype": "Hybrid",
          "core_paradigm": "Blends technical precision with natural language descriptions, showing a particular strength in integrating legible text into images and handling long, detailed prompts without explicit weighting[9][14].",
          "key_strategies": [
            "Combine descriptive narratives with precise structural details, especially for images requiring accurate text or schematic elements[14].",
            "Utilize the model's ability to handle long, natural language prompts to create complex scenes with high coherence and reduced visual artifacts[14].",
            "Rely on natural phrasing to integrate text-image elements seamlessly, as the model is optimized for this over keyword weighting[9].",
            "Specify compositional views like 'isometric' or 'schematic' for operational or technical designs to ensure structural consistency[14]."
          ],
          "example": "A detailed schematic of a futuristic drone assembly line, including annotated components and workflow arrows, in an isometric view with clean lines and legible labels."
        }
      }
    }